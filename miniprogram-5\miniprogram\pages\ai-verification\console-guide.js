// 云开发控制台操作指导
// 帮助用户通过控制台创建术语词典集合

const consoleGuide = {
  
  // 显示创建集合的详细指导
  showCreateCollectionGuide() {
    const guide = `
📋 **创建术语词典集合指导**

由于微信小程序端无法直接创建数据库集合，请按以下步骤操作：

**方法一：通过云开发控制台创建**
1️⃣ 打开微信开发者工具
2️⃣ 点击工具栏的"云开发"按钮
3️⃣ 进入云开发控制台
4️⃣ 选择"数据库"选项卡
5️⃣ 点击"创建集合"
6️⃣ 输入集合名称：terminology_dictionary
7️⃣ 点击确定创建

**方法二：上传云函数自动创建**
1️⃣ 确保 create-terminology 云函数已上传
2️⃣ 在云开发控制台的"云函数"中找到该函数
3️⃣ 点击"测试"按钮执行函数
4️⃣ 函数会自动创建集合并添加数据

**方法三：手动添加数据**
如果集合已存在但无数据：
1️⃣ 进入 terminology_dictionary 集合
2️⃣ 点击"添加记录"
3️⃣ 复制下方的JSON数据并粘贴
4️⃣ 点击确定保存

**术语词典数据（JSON格式）：**
`;

    return guide;
  },

  // 获取术语词典的JSON数据
  getTerminologyJSON() {
    return {
      "version": "1.0",
      "created_at": new Date().toISOString(),
      "categories": {
        "liuyao": [
          "世爻", "应爻", "用神", "原神", "忌神", "仇神",
          "妻财爻", "官鬼爻", "父母爻", "兄弟爻", "子孙爻",
          "动爻", "静爻", "变爻", "飞神", "伏神",
          "月建", "日辰", "旬空", "月破", "暗动",
          "进神", "退神", "反吟", "伏吟", "三合",
          "六合", "六冲", "三刑", "自刑", "墓库"
        ],
        "meihua": [
          "体卦", "用卦", "互卦", "变卦", "本卦",
          "上卦", "下卦", "动爻", "静爻", "体用",
          "生克", "比和", "内卦", "外卦", "主卦",
          "客卦", "时间起卦", "数字起卦", "方位起卦",
          "声音起卦", "颜色起卦", "字数起卦"
        ],
        "bazi": [
          "日主", "日元", "日干", "用神", "喜神",
          "忌神", "仇神", "闲神", "十神", "正官",
          "偏官", "正财", "偏财", "食神", "伤官",
          "比肩", "劫财", "正印", "偏印", "大运",
          "流年", "月令", "格局", "身强", "身弱",
          "从格", "化格", "专旺格", "十二长生"
        ],
        "ziwei": [
          "命宫", "身宫", "兄弟宫", "夫妻宫", "子女宫",
          "财帛宫", "疾厄宫", "迁移宫", "奴仆宫", "官禄宫",
          "田宅宫", "福德宫", "父母宫", "十四主星", "紫微",
          "天机", "太阳", "武曲", "天同", "廉贞",
          "天府", "太阴", "贪狼", "巨门", "天相",
          "天梁", "七杀", "破军", "四化", "化禄",
          "化权", "化科", "化忌", "大限", "小限"
        ]
      },
      "description": "传统占卜学专业术语词典，包含六爻、梅花易数、八字、紫微斗数四大体系的核心术语"
    };
  },

  // 显示完整的操作指导
  showCompleteGuide() {
    const jsonData = JSON.stringify(this.getTerminologyJSON(), null, 2);
    const guide = this.showCreateCollectionGuide();
    
    return `${guide}

\`\`\`json
${jsonData}
\`\`\`

**完成后的验证：**
✅ 集合创建成功
✅ 数据添加完成
✅ 返回小程序重新测试

**注意事项：**
• 确保JSON格式正确，不要有语法错误
• 集合名称必须是：terminology_dictionary
• 数据中的version字段必须是："1.0"

**如果遇到问题：**
1. 检查云开发环境是否正确
2. 确认有数据库操作权限
3. 重新上传云函数并测试
4. 联系技术支持获取帮助
`;
  },

  // 检查集合状态并给出建议
  async checkAndAdvise() {
    try {
      const db = wx.cloud.database();
      
      // 尝试查询集合
      const result = await db.collection('terminology_dictionary')
        .where({ version: '1.0' })
        .get();
      
      if (result.data.length > 0) {
        return {
          status: 'success',
          message: '✅ 术语词典已存在且有数据',
          advice: '系统正常，无需额外操作'
        };
      } else {
        return {
          status: 'empty',
          message: '⚠️ 集合存在但无数据',
          advice: '请按指导添加术语数据'
        };
      }
      
    } catch (error) {
      if (error.message.includes('collection not exists')) {
        return {
          status: 'not_exists',
          message: '❌ 术语词典集合不存在',
          advice: '请按指导创建集合和数据'
        };
      } else {
        return {
          status: 'error',
          message: `❌ 检查失败: ${error.message}`,
          advice: '请检查云开发环境配置'
        };
      }
    }
  }
};

// 导出控制台指导对象
module.exports = consoleGuide;
