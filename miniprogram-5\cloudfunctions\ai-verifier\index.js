// 云函数：ai-verifier
// AI分析结果验证系统，确保每个信息、建议、推断都精准无误

const cloud = require('wx-server-sdk');
const fetch = require('node-fetch');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// DeepSeek API配置
const DEEPSEEK_API_KEY = '***********************************';
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { 
    action = 'verify_analysis',
    analysis_type = 'liuyao',
    user_question = '',
    divination_result = {},
    analysis_content = ''
  } = event;
  
  console.log(`AI验证系统被调用: ${action}, 类型: ${analysis_type}`);
  
  try {
    switch (action) {
      case 'test_api':
        return await testDeepSeekAPI();
      case 'verify_analysis':
        return await verifyAnalysisAccuracy(analysis_type, user_question, divination_result, analysis_content);
      case 'enhance_analysis':
        return await enhanceAnalysisWithKnowledge(analysis_type, user_question, divination_result);
      case 'validate_terminology':
        return await validateTerminologyUsage(analysis_content);
      case 'test_knowledge_base':
        return await testKnowledgeBaseIntegration();
      default:
        return { success: false, error: '未知的验证操作' };
    }
  } catch (error) {
    console.error('AI验证失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 测试DeepSeek API连接
 */
async function testDeepSeekAPI() {
  try {
    console.log('🔍 测试DeepSeek API连接...');

    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: '请回复"连接成功"'
          }
        ],
        temperature: 0.1,
        max_tokens: 50
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.choices && result.choices.length > 0) {
      const reply = result.choices[0].message.content;
      console.log('✅ API连接成功，回复:', reply);

      return {
        success: true,
        reply: reply,
        usage: result.usage
      };
    } else {
      throw new Error('API响应格式异常');
    }

  } catch (error) {
    console.error('❌ API连接失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 验证分析结果的准确性
 */
async function verifyAnalysisAccuracy(analysisType, userQuestion, divinationResult, analysisContent) {
  console.log(`开始验证${analysisType}分析结果的准确性...`);
  
  try {
    // 1. 从知识库获取相关理论依据
    const knowledgeBase = await getRelevantKnowledge(analysisType, userQuestion);
    
    // 2. 构建验证提示词
    const verificationPrompt = buildVerificationPrompt(
      analysisType, 
      userQuestion, 
      divinationResult, 
      analysisContent, 
      knowledgeBase
    );
    
    // 3. 调用DeepSeek API进行验证
    const verificationResult = await callDeepSeekAPI(verificationPrompt);
    
    // 4. 解析验证结果
    const parsedResult = parseVerificationResult(verificationResult);
    
    // 5. 记录验证日志
    await logVerificationResult(analysisType, userQuestion, parsedResult);
    
    return {
      success: true,
      verification: {
        accuracy_score: parsedResult.accuracy_score,
        knowledge_compliance: parsedResult.knowledge_compliance,
        terminology_correctness: parsedResult.terminology_correctness,
        prediction_specificity: parsedResult.prediction_specificity,
        issues_found: parsedResult.issues_found,
        suggestions: parsedResult.suggestions,
        enhanced_analysis: parsedResult.enhanced_analysis
      },
      knowledge_base_references: knowledgeBase.references,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    console.error('验证分析准确性失败:', error);
    throw error;
  }
}

/**
 * 从知识库获取相关理论依据
 */
async function getRelevantKnowledge(analysisType, userQuestion) {
  console.log(`从知识库获取${analysisType}相关理论...`);
  
  try {
    // 根据分析类型确定搜索关键词
    const searchKeywords = getSearchKeywords(analysisType, userQuestion);
    
    // 从结构化知识库搜索
    const knowledgeRecords = await db.collection('knowledge_base')
      .where({
        $or: searchKeywords.map(keyword => ({
          content: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        }))
      })
      .field({
        title: true,
        content: true,
        category: true,
        author: true
      })
      .limit(5)
      .get();
    
    // 从术语词典获取专业术语
    let terminologyData = [];
    try {
      const terminology = await db.collection('terminology_dictionary')
        .where({ version: '1.0' })
        .get();
      
      if (terminology.data.length > 0) {
        terminologyData = terminology.data[0].categories[analysisType] || [];
      }
    } catch (error) {
      console.log('术语词典获取失败，使用默认术语');
      terminologyData = getDefaultTerminology(analysisType);
    }
    
    return {
      records: knowledgeRecords.data,
      terminology: terminologyData,
      references: knowledgeRecords.data.map(record => ({
        title: record.title,
        author: record.author,
        category: record.category
      }))
    };
    
  } catch (error) {
    console.error('获取知识库内容失败:', error);
    return {
      records: [],
      terminology: getDefaultTerminology(analysisType),
      references: []
    };
  }
}

/**
 * 根据分析类型和问题获取搜索关键词
 */
function getSearchKeywords(analysisType, userQuestion) {
  const baseKeywords = {
    'liuyao': ['六爻', '世爻', '应爻', '用神', '原神', '忌神', '六亲'],
    'meihua': ['梅花易数', '体卦', '用卦', '互卦', '变卦', '体用生克'],
    'bazi': ['八字', '四柱', '十神', '用神', '格局', '大运', '流年'],
    'ziwei': ['紫微斗数', '命宫', '身宫', '主星', '四化', '三方四正']
  };
  
  const questionKeywords = extractQuestionKeywords(userQuestion);
  
  return [...(baseKeywords[analysisType] || []), ...questionKeywords];
}

/**
 * 从用户问题中提取关键词
 */
function extractQuestionKeywords(question) {
  const keywords = [];
  
  // 财运相关
  if (question.includes('财运') || question.includes('投资') || question.includes('赚钱')) {
    keywords.push('妻财', '财星', '财帛宫');
  }
  
  // 事业相关
  if (question.includes('工作') || question.includes('事业') || question.includes('升职')) {
    keywords.push('官鬼', '官星', '官禄宫');
  }
  
  // 婚姻相关
  if (question.includes('婚姻') || question.includes('感情') || question.includes('结婚')) {
    keywords.push('夫妻宫', '配偶星');
  }
  
  // 健康相关
  if (question.includes('健康') || question.includes('疾病') || question.includes('身体')) {
    keywords.push('疾厄宫', '身体');
  }
  
  return keywords;
}

/**
 * 获取默认术语（当数据库不可用时）
 */
function getDefaultTerminology(analysisType) {
  const defaultTerms = {
    'liuyao': ['世爻', '应爻', '用神', '原神', '忌神', '仇神', '六亲', '六神'],
    'meihua': ['体卦', '用卦', '互卦', '变卦', '先天数', '后天数'],
    'bazi': ['日主', '用神', '喜神', '忌神', '十神', '格局', '大运', '流年'],
    'ziwei': ['命宫', '身宫', '十四主星', '四化', '三方四正', '大限']
  };
  
  return defaultTerms[analysisType] || [];
}

/**
 * 构建验证提示词
 */
function buildVerificationPrompt(analysisType, userQuestion, divinationResult, analysisContent, knowledgeBase) {
  const knowledgeText = knowledgeBase.records
    .map(record => `《${record.title}》${record.author ? `·${record.author}` : ''}：${record.content.substring(0, 500)}`)
    .join('\n\n');
  
  const terminology = knowledgeBase.terminology.join('、');
  
  return `你是一位精通中国传统占卜学的专家，请严格基于古籍理论验证以下分析结果的准确性。

【占卜类型】：${analysisType}
【用户问题】：${userQuestion}
【占卜结果】：${JSON.stringify(divinationResult, null, 2)}
【当前分析】：${analysisContent}

【知识库依据】：
${knowledgeText}

【专业术语】：${terminology}

请从以下维度进行验证评分（1-10分）：
1. 准确性评分：分析是否符合传统理论
2. 知识库符合度：是否严格基于古籍内容
3. 术语正确性：专业术语使用是否准确
4. 预测具体性：是否给出具体时间和建议

请以JSON格式返回验证结果：
{
  "accuracy_score": 分数,
  "knowledge_compliance": 分数,
  "terminology_correctness": 分数,
  "prediction_specificity": 分数,
  "issues_found": ["发现的问题1", "发现的问题2"],
  "suggestions": ["改进建议1", "改进建议2"],
  "enhanced_analysis": "基于知识库的增强分析内容"
}`;
}

/**
 * 调用DeepSeek API
 */
async function callDeepSeekAPI(prompt) {
  console.log('调用DeepSeek API进行验证...');
  
  try {
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000
      })
    });
    
    if (!response.ok) {
      throw new Error(`DeepSeek API请求失败: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('DeepSeek API返回格式异常');
    }
    
    return data.choices[0].message.content;
    
  } catch (error) {
    console.error('DeepSeek API调用失败:', error);
    throw error;
  }
}

/**
 * 解析验证结果
 */
function parseVerificationResult(apiResponse) {
  try {
    // 尝试解析JSON
    const jsonMatch = apiResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // 如果无法解析JSON，返回默认结构
    return {
      accuracy_score: 7,
      knowledge_compliance: 7,
      terminology_correctness: 8,
      prediction_specificity: 6,
      issues_found: ['API响应格式解析失败'],
      suggestions: ['建议重新验证'],
      enhanced_analysis: apiResponse
    };
    
  } catch (error) {
    console.error('解析验证结果失败:', error);
    return {
      accuracy_score: 5,
      knowledge_compliance: 5,
      terminology_correctness: 5,
      prediction_specificity: 5,
      issues_found: ['验证结果解析失败'],
      suggestions: ['建议检查API响应'],
      enhanced_analysis: '验证过程出现异常，建议重新分析'
    };
  }
}

/**
 * 记录验证日志
 */
async function logVerificationResult(analysisType, userQuestion, verificationResult) {
  try {
    await db.collection('verification_logs').add({
      data: {
        analysis_type: analysisType,
        user_question: userQuestion,
        verification_result: verificationResult,
        timestamp: new Date(),
        accuracy_score: verificationResult.accuracy_score,
        overall_quality: (
          verificationResult.accuracy_score +
          verificationResult.knowledge_compliance +
          verificationResult.terminology_correctness +
          verificationResult.prediction_specificity
        ) / 4
      }
    });
    console.log('验证日志记录成功');
  } catch (error) {
    console.error('验证日志记录失败:', error);
    // 不抛出错误，继续执行
  }
}

/**
 * 使用知识库增强分析结果
 */
async function enhanceAnalysisWithKnowledge(analysisType, userQuestion, divinationResult) {
  console.log(`使用知识库增强${analysisType}分析...`);

  try {
    // 获取相关知识库内容
    const knowledgeBase = await getRelevantKnowledge(analysisType, userQuestion);

    // 构建增强分析提示词
    const enhancePrompt = buildEnhancePrompt(analysisType, userQuestion, divinationResult, knowledgeBase);

    // 调用DeepSeek API
    const enhancedResult = await callDeepSeekAPI(enhancePrompt);

    return {
      success: true,
      enhanced_analysis: enhancedResult,
      knowledge_references: knowledgeBase.references,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('增强分析失败:', error);
    throw error;
  }
}

/**
 * 构建增强分析提示词
 */
function buildEnhancePrompt(analysisType, userQuestion, divinationResult, knowledgeBase) {
  const knowledgeText = knowledgeBase.records
    .map(record => `《${record.title}》：${record.content.substring(0, 800)}`)
    .join('\n\n');

  return `你是一位精通中国传统占卜学的大师，请严格基于古籍理论为用户提供精准的占卜分析。

【占卜类型】：${analysisType}
【用户问题】：${userQuestion}
【占卜结果】：${JSON.stringify(divinationResult, null, 2)}

【古籍理论依据】：
${knowledgeText}

请严格按照以下要求进行分析：

1. **必须基于古籍理论**：所有分析都要有明确的理论依据
2. **精准具体预测**：必须给出具体的时间、数量、结果
3. **专业术语准确**：使用正确的传统术语
4. **问题导向分析**：针对用户具体问题进行定制化分析

分析格式要求：
- 卦象解读：基于古籍的专业解读
- 问题分析：针对用户问题的具体分析
- 时间预测：给出具体的时间点（最多3个选择）
- 行动建议：可操作的具体建议
- 理论依据：引用的古籍理论

请提供完整、专业、精准的分析结果。`;
}

/**
 * 验证术语使用的正确性
 */
async function validateTerminologyUsage(analysisContent) {
  console.log('验证术语使用正确性...');

  try {
    // 获取术语词典
    let terminologyDict = {};
    try {
      const terminology = await db.collection('terminology_dictionary')
        .where({ version: '1.0' })
        .get();

      if (terminology.data.length > 0) {
        terminologyDict = terminology.data[0].categories;
      }
    } catch (error) {
      console.log('术语词典获取失败，使用默认术语');
      terminologyDict = {
        '梅花易数': ['体卦', '用卦', '互卦', '变卦'],
        '六爻': ['世爻', '应爻', '用神', '原神'],
        '八字': ['日主', '用神', '十神', '格局'],
        '紫微': ['命宫', '身宫', '主星', '四化']
      };
    }

    // 分析内容中的术语使用
    const usedTerms = [];
    const incorrectTerms = [];

    Object.keys(terminologyDict).forEach(category => {
      terminologyDict[category].forEach(term => {
        if (analysisContent.includes(term)) {
          usedTerms.push({ term, category });
        }
      });
    });

    return {
      success: true,
      validation: {
        used_terms: usedTerms,
        incorrect_terms: incorrectTerms,
        terminology_score: usedTerms.length > 0 ? 9 : 6,
        suggestions: usedTerms.length === 0 ? ['建议使用更多专业术语'] : ['术语使用良好']
      }
    };

  } catch (error) {
    console.error('术语验证失败:', error);
    throw error;
  }
}

/**
 * 测试知识库集成状态
 */
async function testKnowledgeBaseIntegration() {
  console.log('测试知识库集成状态...');

  try {
    // 测试各个集合的状态
    const knowledgeCount = await db.collection('knowledge_base').count();

    let terminologyCount = { total: 0 };
    try {
      terminologyCount = await db.collection('terminology_dictionary').count();
    } catch (error) {
      console.log('术语词典集合不存在');
    }

    let indexCount = { total: 0 };
    try {
      indexCount = await db.collection('search_indexes').count();
    } catch (error) {
      console.log('搜索索引集合不存在');
    }

    // 测试DeepSeek API连接
    let apiStatus = 'unknown';
    try {
      const testResult = await callDeepSeekAPI('测试连接，请回复"连接成功"');
      apiStatus = testResult.includes('连接成功') || testResult.includes('成功') ? 'connected' : 'response_received';
    } catch (error) {
      apiStatus = 'failed';
    }

    return {
      success: true,
      integration_status: {
        knowledge_base: {
          status: knowledgeCount.total > 0 ? 'ready' : 'empty',
          count: knowledgeCount.total
        },
        terminology_dictionary: {
          status: terminologyCount.total > 0 ? 'ready' : 'missing',
          count: terminologyCount.total
        },
        search_indexes: {
          status: indexCount.total > 0 ? 'ready' : 'missing',
          count: indexCount.total
        },
        deepseek_api: {
          status: apiStatus,
          api_key_configured: DEEPSEEK_API_KEY ? 'yes' : 'no'
        },
        overall_readiness: (
          knowledgeCount.total > 0 &&
          terminologyCount.total > 0 &&
          apiStatus !== 'failed'
        ) ? 'ready' : 'partial'
      }
    };

  } catch (error) {
    console.error('知识库集成测试失败:', error);
    throw error;
  }
}
