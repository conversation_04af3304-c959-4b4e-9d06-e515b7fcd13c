/* pages/meihua/meihua.wxss - 梅花易数页面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.meihua-container {
  min-height: 100vh;
  padding: 40rpx 30rpx;
  font-family: 'STSong', '华文宋体', serif;
}

/* 通用区块样式 */
.question-section,
.method-section,
.input-section {
  background: var(--paper-white);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.section-title {
  color: var(--ink-black);
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  letter-spacing: 1rpx;
}

/* 方法选择器 */
.method-picker {
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  background: var(--ancient-paper);
  border-radius: 12rpx;
  border: 2rpx solid var(--ink-light);
  position: relative;
}

.method-name {
  color: var(--ink-black);
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.method-desc {
  color: var(--ink-gray);
  font-size: 24rpx;
  line-height: 1.4;
}

.picker-arrow {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  color: var(--ink-gray);
  font-size: 24rpx;
}

/* 输入提示 */
.input-tip {
  color: var(--ink-gray);
  font-size: 24rpx;
  margin-top: 16rpx;
  text-align: center;
  line-height: 1.4;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  font-size: 48rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.header-subtitle {
  font-size: 28rpx;
  color: var(--ink-gray);
  font-weight: 400;
}

/* 时间显示区域 */
.time-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
  text-align: center;
}

.time-label {
  font-size: 28rpx;
  color: var(--ink-gray);
  margin-bottom: 20rpx;
}

.time-display {
  font-size: 36rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 20rpx;
  font-family: 'Courier New', monospace;
}

.time-tip {
  font-size: 24rpx;
  color: var(--ink-light);
  line-height: 1.5;
}

/* 起卦按钮 */
.action-section {
  margin-bottom: 40rpx;
}

.start-button {
  background: linear-gradient(135deg, var(--ink-black) 0%, #333333 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 12rpx 40rpx rgba(26, 26, 26, 0.15);
}

.button-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.button-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}



/* 分析中动画 */
.analyzing-section {
  text-align: center;
  padding: 80rpx 40rpx;
}

.analyzing-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}

.ink-drop {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: var(--ink-black);
  margin: 0 8rpx;
  animation: inkPulse 1.5s ease-in-out infinite;
}

.ink-drop:nth-child(2) {
  animation-delay: 0.3s;
}

.ink-drop:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes inkPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

.analyzing-text {
  font-size: 28rpx;
  color: var(--ink-gray);
}

/* 卦象显示 */
.hexagram-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.hexagram-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.hexagram-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 12rpx;
}

.hexagram-question {
  font-size: 26rpx;
  color: var(--ancient-gold);
  margin-bottom: 8rpx;
  font-weight: 500;
}

.hexagram-time {
  font-size: 24rpx;
  color: var(--ink-gray);
}

.hexagram-display {
  text-align: center;
}

.trigram-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.trigram {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  border-radius: 16rpx;
  background: var(--ancient-paper);
  min-width: 200rpx;
}

.upper-trigram {
  border: 2rpx solid var(--ink-black);
}

.lower-trigram {
  border: 2rpx solid var(--ink-gray);
}

.trigram-symbol {
  font-size: 60rpx;
  color: var(--ink-black);
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.trigram-name {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 8rpx;
}

.trigram-label {
  font-size: 22rpx;
  color: var(--ink-gray);
}

.hexagram-center {
  padding: 20rpx;
}

.change-line {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 600;
  background: rgba(212, 175, 55, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 分析结果 */
.analysis-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.analysis-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.analysis-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  text-align: center;
}

.analysis-content {
  line-height: 1.8;
}

.analysis-text {
  font-size: 28rpx;
  color: var(--ink-black);
  white-space: pre-line;
}

/* 底部操作按钮 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.action-button {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.1);
}

.secondary-button {
  background: white;
  border: 2rpx solid var(--ink-gray);
}

.action-text {
  font-size: 28rpx;
  color: var(--ink-gray);
  font-weight: 500;
}

/* ========== 多轮对话界面样式 ========== */

.conversation-section {
  background: var(--paper-white);
  border-radius: 16rpx;
  margin-top: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
  overflow: hidden;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, var(--ancient-paper) 0%, var(--paper-white) 100%);
  border-bottom: 2rpx solid var(--ink-light);
}

.conversation-title {
  color: var(--ink-black);
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.conversation-close {
  color: var(--ink-gray);
  font-size: 40rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 50%;
  background: rgba(26, 26, 26, 0.05);
}

.conversation-container {
  max-height: 600rpx;
  overflow-y: auto;
}

.conversation-messages {
  padding: 24rpx 32rpx;
}

.message-item {
  display: flex;
  margin-bottom: 24rpx;
  align-items: flex-start;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--ancient-paper);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin: 0 16rpx;
  flex-shrink: 0;
}

.message-item.user .message-avatar {
  background: var(--ink-light);
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-text {
  background: var(--ancient-paper);
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: var(--ink-black);
  line-height: 1.6;
  white-space: pre-line;
}

.message-item.user .message-text {
  background: var(--ink-light);
  color: white;
}

.message-time {
  font-size: 20rpx;
  color: var(--ink-gray);
  margin-top: 8rpx;
  text-align: right;
}

.message-item.user .message-time {
  text-align: left;
}

/* AI打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: var(--ancient-paper);
  border-radius: 16rpx;
}

.typing-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: var(--ink-gray);
  margin-right: 8rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) {
  animation-delay: 0s;
  margin-right: 0;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 对话输入区域 */
.conversation-input-area {
  border-top: 2rpx solid var(--ink-light);
  padding: 24rpx 32rpx;
  background: var(--paper-white);
}

.input-container {
  display: flex;
  align-items: center;
  background: var(--ancient-paper);
  border-radius: 32rpx;
  padding: 8rpx;
}

.conversation-input {
  flex: 1;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: var(--ink-black);
  background: transparent;
  border: none;
  outline: none;
}

.send-button {
  background: var(--ink-black);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  margin-left: 16rpx;
}
