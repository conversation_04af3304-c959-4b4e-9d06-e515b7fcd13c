// 本地化快速修复 - 不依赖云函数
// 直接在小程序端解决所有问题

const localFix = {
  
  // 一键本地修复
  async fixAllLocal() {
    console.log('🔧 开始本地化修复...');
    
    const results = {
      terminology_fix: await this.createTerminologyLocal(),
      knowledge_test: await this.testKnowledgeLocal(),
      system_check: await this.checkSystemLocal()
    };
    
    console.log('🎯 本地修复结果:', results);
    
    const allSuccess = Object.values(results).every(result => result.success);
    
    return {
      success: allSuccess,
      results: results,
      summary: {
        terminology_status: results.terminology_fix.success ? '已创建' : '创建失败',
        knowledge_status: results.knowledge_test.success ? `${results.knowledge_test.count}条记录` : '查询失败',
        system_status: results.system_check.success ? '系统就绪' : '存在问题',
        overall_status: allSuccess ? '修复完成' : '部分问题'
      }
    };
  },

  // 本地创建术语词典
  async createTerminologyLocal() {
    console.log('📖 检查术语词典状态...');

    try {
      const db = wx.cloud.database();

      // 注意：小程序端无法直接创建集合，需要通过云开发控制台创建
      console.log('ℹ️ 小程序端无法直接创建集合，检查现有状态...');
      
      // 检查现有数据
      let existing;
      try {
        existing = await db.collection('terminology_dictionary')
          .where({ version: '1.0' })
          .get();

        if (existing.data.length > 0) {
          console.log('✅ 术语词典已存在');
          return {
            success: true,
            total_terms: Object.keys(existing.data[0].categories).reduce((sum, key) =>
              sum + existing.data[0].categories[key].length, 0),
            categories: Object.keys(existing.data[0].categories),
            status: 'already_exists'
          };
        } else {
          console.log('⚠️ 术语词典集合存在但无数据，需要手动添加');
          return {
            success: false,
            error: '术语词典集合存在但无数据，请通过云开发控制台添加数据',
            status: 'empty_collection',
            instruction: '请在云开发控制台的数据库中为 terminology_dictionary 集合添加数据'
          };
        }
      } catch (queryError) {
        console.log('❌ 术语词典集合不存在:', queryError.message);

        // 集合不存在，返回创建指导
        return {
          success: false,
          error: '术语词典集合不存在',
          status: 'collection_not_exists',
          instruction: '请在云开发控制台创建 terminology_dictionary 集合',
          solution: {
            step1: '打开微信开发者工具',
            step2: '进入云开发控制台',
            step3: '在数据库中创建 terminology_dictionary 集合',
            step4: '添加术语数据或上传云函数自动创建'
          }
        };
      }
      
      // 这部分代码不会执行到，因为上面已经处理了所有情况
      
    } catch (error) {
      console.error('❌ 术语词典创建失败:', error);
      return { 
        success: false, 
        error: error.message,
        error_code: error.errCode || 'unknown'
      };
    }
  },

  // 本地测试知识库
  async testKnowledgeLocal() {
    console.log('📚 本地测试知识库...');
    
    try {
      const db = wx.cloud.database();
      
      const knowledgeCount = await db.collection('knowledge_base').count();
      console.log(`📊 知识库记录数: ${knowledgeCount.total}`);
      
      if (knowledgeCount.total > 0) {
        const sampleRecords = await db.collection('knowledge_base')
          .field({ title: true, content: true, category: true })
          .limit(3)
          .get();
        
        console.log('📖 样本记录:', sampleRecords.data.length, '条');
        
        return {
          success: true,
          count: knowledgeCount.total,
          samples: sampleRecords.data.length,
          status: 'available'
        };
      } else {
        return { 
          success: false, 
          error: '知识库为空',
          count: 0
        };
      }
      
    } catch (error) {
      console.error('❌ 知识库测试失败:', error);
      return { 
        success: false, 
        error: error.message,
        count: 0
      };
    }
  },

  // 本地系统检查
  async checkSystemLocal() {
    console.log('🔍 本地系统检查...');
    
    try {
      // 检查云开发环境
      const envCheck = wx.cloud.getCloudEnv ? wx.cloud.getCloudEnv() : 'unknown';
      
      // 检查数据库连接
      const db = wx.cloud.database();
      const collections = ['knowledge_base', 'terminology_dictionary'];
      
      const collectionStatus = {};
      for (const collection of collections) {
        try {
          const count = await db.collection(collection).count();
          collectionStatus[collection] = {
            exists: true,
            count: count.total
          };
        } catch (error) {
          collectionStatus[collection] = {
            exists: false,
            error: error.message
          };
        }
      }
      
      console.log('📊 集合状态:', collectionStatus);
      
      return {
        success: true,
        environment: envCheck,
        collections: collectionStatus,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ 系统检查失败:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  },

  // 显示修复状态
  showLocalFixStatus(results) {
    const status = results.summary;
    
    let message = `本地化修复状态：\n\n`;
    message += `📖 术语词典: ${status.terminology_status}\n`;
    message += `📚 知识库: ${status.knowledge_status}\n`;
    message += `🔍 系统状态: ${status.system_status}\n`;
    message += `🎯 整体状态: ${status.overall_status}\n\n`;
    
    if (results.success) {
      message += `✅ 系统修复完成，可以正常使用！`;
    } else {
      message += `⚠️ 部分功能存在问题，但基础功能可用。`;
    }
    
    return message;
  }
};

// 导出本地修复对象
module.exports = localFix;
