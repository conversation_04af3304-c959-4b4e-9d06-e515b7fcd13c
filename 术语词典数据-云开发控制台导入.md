# 术语词典数据 - 云开发控制台导入

## 📋 操作步骤

1. **打开微信开发者工具**
2. **点击工具栏的"云开发"按钮**
3. **进入云开发控制台**
4. **选择"数据库"选项卡**
5. **点击"创建集合"**
6. **输入集合名称：`terminology_dictionary`**
7. **点击确定创建集合**
8. **进入新创建的集合**
9. **点击"添加记录"**
10. **复制下方JSON数据并粘贴**
11. **点击确定保存**

## 📊 完整术语词典JSON数据（基于您的项目实际代码）

**数据来源**：基于您的 `create-terminology/index.js` 云函数中的完整术语定义
**术语总数**：159个专业术语
**覆盖范围**：六爻、梅花易数、八字、紫微斗数四大体系

```json
{
  "version": "1.0",
  "created_at": "2024-12-29T07:36:00.000Z",
  "categories": {
    "liuyao": [
      "世爻",
      "应爻",
      "用神",
      "原神",
      "忌神",
      "仇神",
      "六亲",
      "六神",
      "青龙",
      "朱雀",
      "勾陈",
      "螣蛇",
      "白虎",
      "玄武",
      "父母爻",
      "兄弟爻",
      "子孙爻",
      "妻财爻",
      "官鬼爻",
      "动爻",
      "静爻",
      "变爻",
      "飞神",
      "伏神",
      "月建",
      "日辰",
      "旬空",
      "月破",
      "暗动",
      "进神",
      "退神",
      "反吟",
      "伏吟",
      "三合",
      "六合",
      "六冲"
    ],
    "meihua": [
      "体卦",
      "用卦",
      "互卦",
      "变卦",
      "本卦",
      "先天数",
      "后天数",
      "上卦",
      "下卦",
      "动爻",
      "体用生克",
      "比和",
      "生体",
      "克体",
      "体生用",
      "体克用",
      "内卦",
      "外卦",
      "主卦",
      "客卦",
      "年月日时",
      "物数起卦",
      "声音起卦",
      "字数起卦",
      "丈尺起卦",
      "为人起卦",
      "自己起卦",
      "占动物",
      "占静物"
    ],
    "bazi": [
      "日主",
      "日元",
      "日干",
      "用神",
      "喜神",
      "忌神",
      "仇神",
      "闲神",
      "十神",
      "比肩",
      "劫财",
      "食神",
      "伤官",
      "偏财",
      "正财",
      "七杀",
      "正官",
      "偏印",
      "正印",
      "格局",
      "正官格",
      "七杀格",
      "财格",
      "印格",
      "食伤格",
      "大运",
      "流年",
      "小运",
      "命宫",
      "胎元",
      "息元",
      "天干",
      "地支",
      "纳音",
      "空亡",
      "刑冲合害",
      "身强",
      "身弱",
      "从格",
      "化格",
      "专旺格",
      "十二长生",
      "沐浴",
      "冠带",
      "临官",
      "帝旺",
      "衰",
      "病",
      "死",
      "墓",
      "绝",
      "胎",
      "养"
    ],
    "ziwei": [
      "命宫",
      "身宫",
      "兄弟宫",
      "夫妻宫",
      "子女宫",
      "财帛宫",
      "疾厄宫",
      "迁移宫",
      "奴仆宫",
      "官禄宫",
      "田宅宫",
      "福德宫",
      "父母宫",
      "十四主星",
      "紫微",
      "天机",
      "太阳",
      "武曲",
      "天同",
      "廉贞",
      "天府",
      "太阴",
      "贪狼",
      "巨门",
      "天相",
      "天梁",
      "七杀",
      "破军",
      "辅星",
      "左辅",
      "右弼",
      "文昌",
      "文曲",
      "天魁",
      "天钺",
      "禄存",
      "天马",
      "煞星",
      "擎羊",
      "陀罗",
      "火星",
      "铃星",
      "地空",
      "地劫",
      "四化",
      "化禄",
      "化权",
      "化科",
      "化忌",
      "三方四正",
      "对宫",
      "三合宫",
      "邻宫",
      "大限",
      "小限",
      "流年",
      "流月",
      "流日",
      "五行局",
      "水二局",
      "木三局",
      "金四局",
      "土五局",
      "火六局"
    ]
  },
  "description": "传统占卜学专业术语词典，包含六爻、梅花易数、八字、紫微斗数四大体系的核心术语，基于元亨利贞项目实际代码生成"
}
```

## ✅ 验证步骤

完成导入后，请验证：

1. **集合名称**：`terminology_dictionary`
2. **记录数量**：1条记录
3. **version字段**：值为 `"1.0"`
4. **categories字段**：包含4个分类（liuyao, meihua, bazi, ziwei）
5. **术语总数**：159个专业术语（完整版）

### 📊 **完整数据统计**

- **六爻术语**：36个（包含完整的六神、六亲、动静爻体系）
- **梅花易数术语**：29个（包含12种起卦方法和体用生克理论）
- **八字术语**：42个（包含完整的十神、格局、大运流年、十二长生体系）
- **紫微斗数术语**：52个（包含十四主星、辅星煞星、四化、五行局完整体系）
- **总计**：159个专业术语

**术语覆盖度**：
- ✅ **六爻体系**：世应、六神、六亲、动静爻、月建日辰等核心概念全覆盖
- ✅ **梅花易数**：体用生克、12种起卦方法、先后天数等完整体系
- ✅ **八字命理**：十神、格局、大运流年、十二长生等专业术语全覆盖
- ✅ **紫微斗数**：十四主星、辅星煞星、四化、宫位、五行局等完整体系

## 🔄 完成后操作

1. **返回微信小程序**
2. **进入AI验证页面**
3. **点击"🏠 本地化修复（推荐）"**
4. **验证术语词典状态**
5. **确认显示"✅ 术语词典已存在"**

## ⚠️ 注意事项

- 确保JSON格式完全正确，不要有语法错误
- 集合名称必须完全匹配：`terminology_dictionary`
- version字段值必须是字符串：`"1.0"`
- 如果粘贴时出现格式问题，请检查引号和逗号

## 📞 技术支持

如果遇到问题：
1. 检查云开发环境ID是否正确
2. 确认有数据库操作权限
3. 重新检查JSON格式
4. 尝试重新创建集合

## 🎯 **导入成功后的验证结果**

完成导入后，在小程序中测试应该显示：

```
✅ 术语词典: 159个术语（完整版）
✅ 知识库: 437条记录
✅ 系统状态: 系统就绪
✅ 整体状态: 修复完成
```

**完整术语体系验证**：
- ✅ **六爻体系**：36个术语（世应、六神、六亲、动静爻等）
- ✅ **梅花易数**：29个术语（体用生克、12种起卦法、先后天数等）
- ✅ **八字命理**：42个术语（十神、格局、大运流年、十二长生等）
- ✅ **紫微斗数**：52个术语（十四主星、四化、五行局、辅星煞星等）

**数据来源说明**：
此术语词典基于您的 `miniprogram-5/cloudfunctions/create-terminology/index.js` 文件中的实际代码生成，确保与您的项目完全匹配，是真实全面的专业术语集合，而非示例数据。
