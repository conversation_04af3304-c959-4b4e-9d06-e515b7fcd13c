/* pages/ziwei/ziwei.wxss - 紫微斗数页面样式 */

.ziwei-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #6d28d9 0%, #a855f7 50%, #7c3aed 100%);
  padding: 20rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* 输入区域 */
.input-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  font-size: 32rpx;
  color: #374151;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.question-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #374151;
  background: #ffffff;
  box-sizing: border-box;
}

.question-input:focus {
  border-color: #7c3aed;
  outline: none;
}

.section-title {
  display: block;
  font-size: 36rpx;
  color: #7c3aed;
  margin: 40rpx 0 20rpx 0;
  font-weight: bold;
}

/* 性别选择 */
.radio-item {
  display: inline-flex;
  align-items: center;
  margin-right: 40rpx;
  font-size: 30rpx;
  color: #374151;
}

.radio-item text {
  margin-left: 10rpx;
}

/* 日期时间选择器 */
.date-picker,
.time-picker {
  width: 100%;
}

.picker-display {
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #374151;
  background: #ffffff;
  min-height: 40rpx;
  display: flex;
  align-items: center;
}

/* 按钮样式 */
.action-section {
  text-align: center;
  margin-top: 40rpx;
}

.start-btn {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(124, 58, 237, 0.3);
  transition: all 0.3s ease;
}

.start-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(124, 58, 237, 0.3);
}

.start-btn.analyzing {
  background: linear-gradient(135deg, #9ca3af, #d1d5db);
  box-shadow: none;
}

.restart-btn {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  margin-top: 30rpx;
}

/* 结果显示区域 */
.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.question-review {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.question-text {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

/* 紫微命盘 */
.ziwei-chart {
  margin-bottom: 40rpx;
}

.chart-title {
  display: block;
  font-size: 36rpx;
  color: #7c3aed;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 10rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  padding: 20rpx;
}

.palace-item {
  position: relative;
  background: #ffffff;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  padding: 15rpx 10rpx;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.palace-ming {
  border-color: #dc2626;
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
}

.palace-shen {
  border-color: #059669;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
}

.palace-major {
  border-color: #7c3aed;
  background: linear-gradient(135deg, #faf5ff, #f3e8ff);
}

.palace-normal {
  border-color: #e5e7eb;
  background: #ffffff;
}

.palace-name {
  font-size: 24rpx;
  color: #374151;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.palace-branch {
  font-size: 20rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.palace-stars {
  font-size: 18rpx;
  color: #7c3aed;
  line-height: 1.3;
  word-break: break-all;
}

.palace-mark {
  position: absolute;
  top: 5rpx;
  right: 5rpx;
  background: #dc2626;
  color: #ffffff;
  font-size: 16rpx;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-weight: bold;
}

/* 基本信息 */
.basic-info {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.info-title {
  display: block;
  font-size: 32rpx;
  color: #7c3aed;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.info-label {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  min-width: 120rpx;
}

.info-value {
  font-size: 28rpx;
  color: #6b7280;
  flex: 1;
}

.info-value.score {
  color: #dc2626;
  font-weight: bold;
}

/* 格局分析 */
.pattern-analysis {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.analysis-title {
  display: block;
  font-size: 32rpx;
  color: #92400e;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.pattern-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.pattern-name {
  font-size: 28rpx;
  color: #92400e;
  font-weight: bold;
  margin-right: 10rpx;
}

.pattern-level {
  font-size: 24rpx;
  color: #059669;
  margin-right: 15rpx;
}

.pattern-meaning {
  font-size: 26rpx;
  color: #374151;
  flex: 1;
  line-height: 1.4;
}

/* 精准问题分析 */
.custom-analysis {
  background: linear-gradient(135deg, #ddd6fe, #c4b5fd);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.custom-analysis .analysis-title {
  color: #5b21b6;
}

.analysis-content {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8rpx;
  padding: 25rpx;
}

.analysis-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
  white-space: pre-line;
}

/* 综合总结 */
.summary {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.summary-title {
  display: block;
  font-size: 32rpx;
  color: #0369a1;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.summary-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
}

/* 真太阳时信息样式 */
.solar-time-info {
  background: linear-gradient(135deg, #f8f8f0, #f0f0e8);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 16rpx;
  border-left: 4rpx solid #7c3aed;
}

.solar-time-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.solar-time-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
  white-space: pre-line;
}

.solar-time-result {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #7c3aed;
}

/* 预分析对话界面样式 */
.conversation-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.conversation-header {
  background: linear-gradient(135deg, #6d28d9 0%, #a855f7 50%, #7c3aed 100%);
  padding: 40rpx 30rpx 30rpx;
  color: white;
  position: relative;
}

.conversation-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.conversation-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

.close-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
}

.conversation-content {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

.conversation-history {
  flex: 1;
  padding: 30rpx;
  max-height: calc(100vh - 300rpx);
}

.message-item {
  margin-bottom: 30rpx;
}

.message {
  max-width: 80%;
  padding: 20rpx 25rpx;
  border-radius: 20rpx;
  position: relative;
}

.ai-message {
  background: linear-gradient(135deg, #6d28d9 0%, #a855f7 50%, #7c3aed 100%);
  color: white;
  margin-right: auto;
  border-bottom-left-radius: 8rpx;
}

.user-message {
  background: #f0f0f0;
  color: #333;
  margin-left: auto;
  border-bottom-right-radius: 8rpx;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 22rpx;
  opacity: 0.7;
  margin-top: 10rpx;
}

.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.typing-dots {
  display: flex;
  margin-right: 15rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: #7c3aed;
  border-radius: 50%;
  margin-right: 8rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 26rpx;
  color: #666;
}

.conversation-input-area {
  border-top: 1rpx solid #eee;
  padding: 20rpx 30rpx;
  background: white;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}

.conversation-input {
  flex: 1;
  min-height: 80rpx;
  max-height: 200rpx;
  padding: 15rpx 20rpx;
  border: 2rpx solid #eee;
  border-radius: 25rpx;
  font-size: 28rpx;
  background: #f9f9f9;
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #6d28d9 0%, #a855f7 50%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}