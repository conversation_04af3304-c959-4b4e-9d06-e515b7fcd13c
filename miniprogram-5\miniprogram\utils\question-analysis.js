// 问题分类和精准分析系统
// 基于传统六爻理论和梅花易数知识库

// 问题类型识别关键词
const QUESTION_KEYWORDS = {
  财运: ['财运', '财富', '赚钱', '收入', '投资', '理财', '股票', '基金', '生意', '买卖', '经商', '盈利', '亏损', '金钱'],
  学业: ['学业', '考试', '升学', '读书', '学习', '成绩', '文凭', '学校', '教育', '培训', '进修'],
  事业: ['工作', '事业', '职业', '升职', '跳槽', '求职', '面试', '职场', '上班', '创业', '开公司'],
  婚姻: ['婚姻', '结婚', '恋爱', '感情', '男友', '女友', '老公', '老婆', '配偶', '另一半', '对象'],
  健康: ['健康', '疾病', '生病', '身体', '医院', '治疗', '康复', '病情', '手术'],
  子女: ['子女', '孩子', '生育', '怀孕', '生子', '儿子', '女儿', '小孩', '宝宝'],
  合伙: ['合伙', '合作', '伙伴', '朋友', '同事', '团队', '协作'],
  桃花: ['桃花', '异性', '追求', '表白', '相亲', '脱单', '恋爱运']
};

// 六亲对应关系
const LIUQIN_MAPPING = {
  财运: '妻财',
  学业: '父母',
  事业: '官鬼',
  子女: '子孙',
  合伙: '兄弟',
  健康: '相应六亲', // 根据具体疾病看相应六亲
  婚姻: { 男: '妻财', 女: '官鬼' },
  桃花: { 男: '妻财', 女: '官鬼' }
};

// 识别问题类型
function identifyQuestionType(question) {
  const questionLower = question.toLowerCase();
  
  for (const [type, keywords] of Object.entries(QUESTION_KEYWORDS)) {
    for (const keyword of keywords) {
      if (questionLower.includes(keyword)) {
        return type;
      }
    }
  }
  
  return '综合'; // 默认类型
}

// 财运专项分析
function analyzeWealth(hexagram, bodyUseAnalysis, questionType) {
  const analysis = {
    focus: '妻财爻',
    timing: '',
    profit: '',
    risk: '',
    advice: ''
  };
  
  // 基于体用生克分析财运
  if (bodyUseAnalysis.result === '吉') {
    if (bodyUseAnalysis.relationship === 'generate') {
      analysis.timing = '当前时机有利，宜在1-3个月内行动';
      analysis.profit = '预期收益较好，可获利20-50%';
      analysis.advice = '用生体，财源广进，适合投资。建议选择稳健型投资项目。';
    }
  } else if (bodyUseAnalysis.result === '凶') {
    if (bodyUseAnalysis.relationship === 'overcome') {
      analysis.timing = '当前不宜投资，建议等待6个月后';
      analysis.risk = '投资风险极高，可能亏损30-70%';
      analysis.advice = '用克体，财运受阻，强行投资必有损失。建议保守理财，等待时机。';
    }
  }
  
  // 根据卦象五行属性细化分析
  const upperElement = getTrigramElement(hexagram.upper.number);
  const lowerElement = getTrigramElement(hexagram.lower.number);
  
  if (upperElement === '金' || lowerElement === '金') {
    analysis.advice += '金主财，可关注金融、贵金属投资。';
  } else if (upperElement === '水' || lowerElement === '水') {
    analysis.advice += '水主流动，适合短期投资、流动性强的项目。';
  }
  
  return analysis;
}

// 学业专项分析
function analyzeEducation(hexagram, bodyUseAnalysis) {
  const analysis = {
    focus: '父母爻',
    examResult: '',
    studyDirection: '',
    timing: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.examResult = '考试运势良好，有望取得理想成绩';
    analysis.timing = '适合在近期报考或参加重要考试';
    analysis.studyDirection = '宜选择文科或管理类专业';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.examResult = '考试阻力较大，需要加倍努力';
    analysis.timing = '建议推迟考试时间，充分准备后再战';
    analysis.studyDirection = '可考虑技能型或实用性专业';
  }
  
  // 根据卦象特性分析学习方向
  const upperAttrs = getTrigramAttributes(hexagram.upper.number);
  if (upperAttrs.nature.includes('文')) {
    analysis.studyDirection += '，文学、教育类专业有利';
  }
  
  return analysis;
}

// 事业专项分析
function analyzeCareer(hexagram, bodyUseAnalysis) {
  const analysis = {
    focus: '官鬼爻',
    promotion: '',
    jobChange: '',
    timing: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.promotion = '升职机会较好，有贵人相助';
    analysis.jobChange = '适合主动寻求更好的工作机会';
    analysis.timing = '宜在2-4个月内采取行动';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.promotion = '升职阻力较大，需要耐心等待';
    analysis.jobChange = '不宜贸然跳槽，容易遇到困难';
    analysis.timing = '建议等待半年后再做决定';
  }
  
  return analysis;
}

// 婚姻专项分析（需要性别信息）
function analyzeMarriage(hexagram, bodyUseAnalysis, gender = '男') {
  const analysis = {
    focus: gender === '男' ? '妻财爻' : '官鬼爻',
    relationship: '',
    timing: '',
    partner: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.relationship = '感情运势良好，有望遇到合适对象';
    analysis.timing = '适合在春夏季节主动寻找感情';
    analysis.partner = '对象可能是温和、有才华的人';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.relationship = '感情路上多波折，需要耐心';
    analysis.timing = '不宜急于求成，建议等待合适时机';
    analysis.partner = '要谨慎选择，避免不合适的对象';
  }
  
  return analysis;
}

// 健康专项分析
function analyzeHealth(hexagram, bodyUseAnalysis, question = '') {
  // 根据具体疾病类型确定相应六亲
  let focusLiuqin = '相应六亲';
  let specificFocus = '';

  const questionLower = question.toLowerCase();

  // 根据疾病类型确定六亲
  if (questionLower.includes('心脏') || questionLower.includes('血液') || questionLower.includes('精神')) {
    focusLiuqin = '官鬼爻';
    specificFocus = '心脏、血液、精神类疾病看官鬼爻';
  } else if (questionLower.includes('肠胃') || questionLower.includes('消化') || questionLower.includes('脾胃')) {
    focusLiuqin = '妻财爻';
    specificFocus = '肠胃、消化系统疾病看妻财爻';
  } else if (questionLower.includes('肺') || questionLower.includes('呼吸') || questionLower.includes('皮肤')) {
    focusLiuqin = '父母爻';
    specificFocus = '肺部、呼吸系统、皮肤疾病看父母爻';
  } else if (questionLower.includes('肝') || questionLower.includes('筋骨') || questionLower.includes('四肢')) {
    focusLiuqin = '兄弟爻';
    specificFocus = '肝脏、筋骨、四肢疾病看兄弟爻';
  } else if (questionLower.includes('肾') || questionLower.includes('生殖') || questionLower.includes('泌尿')) {
    focusLiuqin = '子孙爻';
    specificFocus = '肾脏、生殖、泌尿系统疾病看子孙爻';
  } else {
    // 一般疾病看官鬼爻（病神）
    focusLiuqin = '官鬼爻';
    specificFocus = '一般疾病看官鬼爻（病神）';
  }

  const analysis = {
    focus: focusLiuqin,
    specificFocus: specificFocus,
    condition: '',
    recovery: '',
    prevention: '',
    advice: ''
  };

  if (bodyUseAnalysis.result === '吉') {
    analysis.condition = '身体状况良好，疾病有望康复';
    analysis.recovery = '恢复期较短，约1-2个月';
    analysis.prevention = '注意日常保养，可预防疾病';
    analysis.advice = '体用相生，身体康复有望，宜配合治疗，注意调养。';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.condition = '需要重视健康问题，及时就医';
    analysis.recovery = '恢复期较长，需要耐心治疗';
    analysis.prevention = '要特别注意预防，避免病情加重';
    analysis.advice = '体用相克，病情较重，必须及时就医，不可拖延。';
  } else {
    analysis.condition = '身体状况平稳，需要持续关注';
    analysis.recovery = '恢复期中等，约2-3个月';
    analysis.prevention = '保持良好生活习惯，定期检查';
    analysis.advice = '体用平和，病情稳定，宜静养调理，循序渐进。';
  }

  return analysis;
}

// 合伙专项分析
function analyzePartnership(hexagram, bodyUseAnalysis) {
  const analysis = {
    focus: '兄弟爻',
    cooperation: '',
    profit: '',
    risk: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.cooperation = '合作关系和谐，有利共同发展';
    analysis.profit = '合伙有利，可实现共赢';
    analysis.risk = '风险较小，但需要明确分工';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.cooperation = '合作中容易产生分歧和矛盾';
    analysis.profit = '利益分配可能不均，需要谨慎';
    analysis.risk = '合伙风险较大，建议重新考虑';
  }
  
  return analysis;
}

// 获取八卦五行属性
function getTrigramElement(trigramNumber) {
  const elementMap = {
    1: '金', 2: '金', // 乾、兑
    3: '火',          // 离
    4: '木', 5: '木', // 震、巽
    6: '水',          // 坎
    7: '土', 8: '土'  // 艮、坤
  };
  return elementMap[trigramNumber] || '土';
}

// 获取八卦万物属类（简化版）
function getTrigramAttributes(trigramNumber) {
  // 这里应该引用完整的TRIGRAM_ATTRIBUTES，简化处理
  const simpleAttrs = {
    1: { nature: ['天', '父', '官贵'] },
    2: { nature: ['泽', '少女', '口舌'] },
    3: { nature: ['火', '中女', '文书'] },
    4: { nature: ['雷', '长男', '动'] },
    5: { nature: ['风', '长女', '文'] },
    6: { nature: ['水', '中男', '险'] },
    7: { nature: ['山', '少男', '止'] },
    8: { nature: ['地', '母', '众'] }
  };
  return simpleAttrs[trigramNumber] || { nature: ['未知'] };
}

// 综合分析生成器
function generateCustomAnalysis(question, hexagram, bodyUseAnalysis, mutualHexagram, changedHexagram) {
  const questionType = identifyQuestionType(question);
  let specificAnalysis = {};
  
  switch (questionType) {
    case '财运':
      specificAnalysis = analyzeWealth(hexagram, bodyUseAnalysis, questionType);
      break;
    case '学业':
      specificAnalysis = analyzeEducation(hexagram, bodyUseAnalysis);
      break;
    case '事业':
      specificAnalysis = analyzeCareer(hexagram, bodyUseAnalysis);
      break;
    case '婚姻':
    case '桃花':
      specificAnalysis = analyzeMarriage(hexagram, bodyUseAnalysis);
      break;
    case '健康':
      specificAnalysis = analyzeHealth(hexagram, bodyUseAnalysis, question);
      break;
    case '合伙':
      specificAnalysis = analyzePartnership(hexagram, bodyUseAnalysis);
      break;
    default:
      specificAnalysis = { focus: '综合分析', advice: '需要综合考虑各方面因素' };
  }
  
  return {
    questionType,
    specificAnalysis,
    liuqin: LIUQIN_MAPPING[questionType] || '综合'
  };
}

// 八字专项分析
function analyzeBaziWealth(bazi, analysis, question = '') {
  const tenGods = analysis.tenGods.distribution;
  const useGod = analysis.useGod.useGod;
  const strength = analysis.strength.level;

  const wealthAnalysis = {
    focus: '财星（正财、偏财）',
    wealthStar: '',
    timing: '',
    profit: '',
    risk: '',
    advice: ''
  };

  // 分析财星情况
  const zhengcai = tenGods['正财'].length;
  const piancai = tenGods['偏财'].length;

  if (zhengcai > 0 && piancai === 0) {
    wealthAnalysis.wealthStar = '正财透干，主正当收入，财运稳定';
    wealthAnalysis.timing = '适合稳健投资，3-6个月见效';
    wealthAnalysis.profit = '收益稳定，预期15-25%';
  } else if (piancai > 0 && zhengcai === 0) {
    wealthAnalysis.wealthStar = '偏财透干，主横财机遇，财运波动';
    wealthAnalysis.timing = '适合短期投资，1-2个月内行动';
    wealthAnalysis.profit = '收益较高，预期25-40%，但风险较大';
  } else if (zhengcai > 0 && piancai > 0) {
    wealthAnalysis.wealthStar = '正偏财并见，财源广进，但需要选择';
    wealthAnalysis.timing = '可分批投资，2-4个月为佳';
    wealthAnalysis.profit = '综合收益20-30%';
  } else {
    wealthAnalysis.wealthStar = '财星不现，求财较难，需要等待时机';
    wealthAnalysis.timing = '暂不宜投资，等待6个月后再看';
    wealthAnalysis.profit = '收益有限，建议保守理财';
  }

  // 根据日主强弱调整建议
  if (strength === '偏弱' || strength === '很弱') {
    wealthAnalysis.risk = '日主偏弱，不胜财星，投资需谨慎';
    wealthAnalysis.advice = '先养身后求财，可考虑合伙投资分散风险';
  } else if (strength === '偏旺') {
    wealthAnalysis.risk = '日主偏旺，能胜财星，可积极求财';
    wealthAnalysis.advice = '财运较好，可适当加大投资力度，把握机会';
  } else {
    wealthAnalysis.risk = '日主中和，财运平稳';
    wealthAnalysis.advice = '稳健投资为主，避免过度冒险';
  }

  return wealthAnalysis;
}

function analyzeBaziCareer(bazi, analysis, question = '') {
  const tenGods = analysis.tenGods.distribution;
  const pattern = analysis.pattern.pattern;

  const careerAnalysis = {
    focus: '官星（正官、七杀）',
    officialStar: '',
    promotion: '',
    jobChange: '',
    timing: '',
    advice: ''
  };

  // 分析官星情况
  const zhengguan = tenGods['正官'].length;
  const qisha = tenGods['七杀'].length;

  if (zhengguan > 0 && qisha === 0) {
    careerAnalysis.officialStar = '正官透干，主正当权威，事业稳步发展';
    careerAnalysis.promotion = '升职前景良好，2-3年内有望';
    careerAnalysis.jobChange = '不宜轻易跳槽，现职发展更佳';
    careerAnalysis.timing = '秋冬季节行动较佳';
  } else if (qisha > 0 && zhengguan === 0) {
    careerAnalysis.officialStar = '七杀透干，主威权魄力，适合开创事业';
    careerAnalysis.promotion = '升职需要竞争，但有实力胜出';
    careerAnalysis.jobChange = '可考虑跳槽到更有挑战性的岗位';
    careerAnalysis.timing = '春夏季节行动力强';
  } else if (zhengguan > 0 && qisha > 0) {
    careerAnalysis.officialStar = '官杀混杂，事业多变，需要选择方向';
    careerAnalysis.promotion = '升职机会多但竞争激烈';
    careerAnalysis.jobChange = '需要慎重选择，避免频繁变动';
    careerAnalysis.timing = '等待明确机会再行动';
  } else {
    careerAnalysis.officialStar = '官星不现，事业发展需要自己创造机会';
    careerAnalysis.promotion = '升职较难，需要长期努力';
    careerAnalysis.jobChange = '可考虑自主创业或技术路线';
    careerAnalysis.timing = '需要耐心等待，3-5年后见效';
  }

  // 根据格局调整建议
  if (pattern.includes('官')) {
    careerAnalysis.advice = '官格命局，适合公职或管理岗位，宜稳步发展';
  } else if (pattern.includes('财')) {
    careerAnalysis.advice = '财格命局，适合商业经营，可考虑创业';
  } else if (pattern.includes('印')) {
    careerAnalysis.advice = '印格命局，适合文教科研，宜深耕专业';
  } else {
    careerAnalysis.advice = '根据个人特长选择发展方向，不拘一格';
  }

  return careerAnalysis;
}

function analyzeBaziMarriage(bazi, analysis, isMale, question = '') {
  const tenGods = analysis.tenGods.distribution;
  const dayMaster = analysis.dayMaster;

  const marriageAnalysis = {
    focus: isMale ? '财星（妻星）' : '官星（夫星）',
    spouseStar: '',
    relationship: '',
    timing: '',
    partner: '',
    advice: ''
  };

  if (isMale) {
    // 男命看财星为妻
    const zhengcai = tenGods['正财'].length;
    const piancai = tenGods['偏财'].length;

    if (zhengcai === 1 && piancai === 0) {
      marriageAnalysis.spouseStar = '正财独透，妻星清纯，婚姻美满';
      marriageAnalysis.relationship = '感情专一，夫妻恩爱';
      marriageAnalysis.timing = '25-30岁之间结婚最佳';
      marriageAnalysis.partner = '妻子贤惠持家，有助夫运';
    } else if (piancai > 0) {
      marriageAnalysis.spouseStar = '偏财透干，异性缘佳，但需要专一';
      marriageAnalysis.relationship = '感情丰富，需要把握分寸';
      marriageAnalysis.timing = '不宜过早结婚，30岁后较稳定';
      marriageAnalysis.partner = '对象活泼外向，但需要磨合';
    } else {
      marriageAnalysis.spouseStar = '财星不现，姻缘较晚，需要主动';
      marriageAnalysis.relationship = '感情发展缓慢，需要耐心';
      marriageAnalysis.timing = '32岁后才有稳定姻缘';
      marriageAnalysis.partner = '缘分需要等待，不可强求';
    }
  } else {
    // 女命看官星为夫
    const zhengguan = tenGods['正官'].length;
    const qisha = tenGods['七杀'].length;

    if (zhengguan === 1 && qisha === 0) {
      marriageAnalysis.spouseStar = '正官独透，夫星清贵，婚姻幸福';
      marriageAnalysis.relationship = '丈夫有责任心，家庭和睦';
      marriageAnalysis.timing = '23-28岁之间结婚最佳';
      marriageAnalysis.partner = '丈夫稳重可靠，有事业心';
    } else if (qisha > 0) {
      marriageAnalysis.spouseStar = '七杀透干，夫星有力，但需要包容';
      marriageAnalysis.relationship = '丈夫性格强势，需要理解';
      marriageAnalysis.timing = '不宜过早结婚，28岁后较好';
      marriageAnalysis.partner = '对象有能力但脾气较急';
    } else {
      marriageAnalysis.spouseStar = '官星不现，姻缘需要等待';
      marriageAnalysis.relationship = '感情路较为波折';
      marriageAnalysis.timing = '30岁后才有正缘';
      marriageAnalysis.partner = '需要通过朋友介绍或相亲';
    }
  }

  marriageAnalysis.advice = '婚姻需要经营，相互理解包容最重要';

  return marriageAnalysis;
}

// 八字综合分析生成器
function generateBaziAnalysis(question, bazi, analysis, isMale = true) {
  const questionType = identifyQuestionType(question);

  let specificAnalysis = {};

  switch (questionType) {
    case '财运':
      specificAnalysis = analyzeBaziWealth(bazi, analysis, question);
      break;
    case '事业':
      specificAnalysis = analyzeBaziCareer(bazi, analysis, question);
      break;
    case '婚姻':
    case '桃花':
      specificAnalysis = analyzeBaziMarriage(bazi, analysis, isMale, question);
      break;
    default:
      specificAnalysis = {
        focus: '综合分析',
        advice: '根据八字格局和用神喜忌综合判断'
      };
  }

  return {
    questionType: questionType,
    specificAnalysis: specificAnalysis,
    dayMaster: analysis.dayMaster,
    pattern: analysis.pattern.pattern,
    strength: analysis.strength.level
  };
}

// 紫微斗数专项分析
function analyzeZiweiWealth(chart, analysis, question = '') {
  const caiboGong = chart['财帛宫'];
  const mingGong = chart['命宫'];

  const wealthAnalysis = {
    focus: '财帛宫星曜配置',
    wealthStars: '',
    timing: '',
    profit: '',
    risk: '',
    advice: ''
  };

  // 分析财帛宫星曜
  const majorStars = caiboGong.stars.filter(s => s.type === 'major').map(s => s.name);
  const auxiliaryStars = caiboGong.stars.filter(s => s.type === 'auxiliary').map(s => s.name);
  const maleficStars = caiboGong.stars.filter(s => s.type === 'malefic').map(s => s.name);

  if (majorStars.includes('武曲')) {
    wealthAnalysis.wealthStars = '武曲入财帛宫，财星得位，财运极佳';
    wealthAnalysis.timing = '适合长期投资，3-5年见大效';
    wealthAnalysis.profit = '收益稳定且丰厚，预期30-50%';
    wealthAnalysis.risk = '风险可控，但需要专业理财';
  } else if (majorStars.includes('太阴')) {
    wealthAnalysis.wealthStars = '太阴入财帛宫，富星照命，财源广进';
    wealthAnalysis.timing = '适合稳健投资，6个月后见效';
    wealthAnalysis.profit = '收益稳定，预期20-35%';
    wealthAnalysis.risk = '风险较低，适合保守投资';
  } else if (majorStars.includes('贪狼')) {
    wealthAnalysis.wealthStars = '贪狼入财帛宫，偏财运强，但波动大';
    wealthAnalysis.timing = '适合短期投机，1-3个月内行动';
    wealthAnalysis.profit = '收益可观但不稳定，预期20-60%';
    wealthAnalysis.risk = '风险较高，需要谨慎把握时机';
  } else if (majorStars.length === 0) {
    wealthAnalysis.wealthStars = '财帛宫无主星，财运平平，需要借助他宫';
    wealthAnalysis.timing = '需要等待时机，建议观望6个月';
    wealthAnalysis.profit = '收益有限，预期5-15%';
    wealthAnalysis.risk = '风险中等，不宜大额投资';
  }

  // 根据辅星调整
  if (auxiliaryStars.includes('禄存')) {
    wealthAnalysis.advice = '禄存同宫，财运亨通，可积极理财';
  } else if (auxiliaryStars.includes('天马')) {
    wealthAnalysis.advice = '天马同宫，财来财去，需要稳健投资';
  } else {
    wealthAnalysis.advice = '财帛宫配置一般，建议稳健理财';
  }

  // 根据煞星调整
  if (maleficStars.length > 0) {
    wealthAnalysis.risk += '，有煞星同宫，需要特别谨慎';
  }

  return wealthAnalysis;
}

function analyzeZiweiCareer(chart, analysis, question = '') {
  const guanluGong = chart['官禄宫'];
  const mingGong = chart['命宫'];

  const careerAnalysis = {
    focus: '官禄宫星曜配置',
    careerStars: '',
    promotion: '',
    jobChange: '',
    timing: '',
    advice: ''
  };

  // 分析官禄宫星曜
  const majorStars = guanluGong.stars.filter(s => s.type === 'major').map(s => s.name);
  const auxiliaryStars = guanluGong.stars.filter(s => s.type === 'auxiliary').map(s => s.name);

  if (majorStars.includes('紫微')) {
    careerAnalysis.careerStars = '紫微入官禄宫，帝星照命，适合管理职位';
    careerAnalysis.promotion = '升职前景极佳，1-2年内有望';
    careerAnalysis.jobChange = '不宜轻易跳槽，现职发展更好';
    careerAnalysis.timing = '秋季行动最佳';
    careerAnalysis.advice = '天生领导才能，适合高层管理';
  } else if (majorStars.includes('天府')) {
    careerAnalysis.careerStars = '天府入官禄宫，库星得位，事业稳固';
    careerAnalysis.promotion = '升职稳步进行，2-3年内可期';
    careerAnalysis.jobChange = '现职稳定，不建议跳槽';
    careerAnalysis.timing = '冬季规划，春季行动';
    careerAnalysis.advice = '适合财务、管理等稳定职业';
  } else if (majorStars.includes('七杀')) {
    careerAnalysis.careerStars = '七杀入官禄宫，将星得位，适合开创事业';
    careerAnalysis.promotion = '升职需要竞争，但有实力胜出';
    careerAnalysis.jobChange = '可考虑跳槽到更有挑战的岗位';
    careerAnalysis.timing = '夏季行动力最强';
    careerAnalysis.advice = '适合军警、企业家等职业';
  } else {
    careerAnalysis.careerStars = '官禄宫无主星，事业需要借助他宫力量';
    careerAnalysis.promotion = '升职较慢，需要长期努力';
    careerAnalysis.jobChange = '可考虑转换跑道寻找机会';
    careerAnalysis.timing = '需要耐心等待时机';
    careerAnalysis.advice = '需要找到适合的发展方向';
  }

  // 根据辅星调整
  if (auxiliaryStars.includes('文昌') || auxiliaryStars.includes('文曲')) {
    careerAnalysis.advice += '，有文星助力，适合文教行业';
  }

  if (auxiliaryStars.includes('左辅') || auxiliaryStars.includes('右弼')) {
    careerAnalysis.advice += '，有贵人相助，事业顺利';
  }

  return careerAnalysis;
}

function analyzeZiweiMarriage(chart, analysis, isMale, question = '') {
  const fuqiGong = chart['夫妻宫'];
  const mingGong = chart['命宫'];

  const marriageAnalysis = {
    focus: '夫妻宫星曜配置',
    spouseStars: '',
    relationship: '',
    timing: '',
    partner: '',
    advice: ''
  };

  // 分析夫妻宫星曜
  const majorStars = fuqiGong.stars.filter(s => s.type === 'major').map(s => s.name);
  const auxiliaryStars = fuqiGong.stars.filter(s => s.type === 'auxiliary').map(s => s.name);
  const maleficStars = fuqiGong.stars.filter(s => s.type === 'malefic').map(s => s.name);

  if (majorStars.includes('太阴')) {
    marriageAnalysis.spouseStars = '太阴入夫妻宫，配偶温柔贤惠';
    marriageAnalysis.relationship = '感情和睦，夫妻恩爱';
    marriageAnalysis.timing = '25-30岁之间结婚最佳';
    marriageAnalysis.partner = '配偶外貌清秀，性格温和';
  } else if (majorStars.includes('天同')) {
    marriageAnalysis.spouseStars = '天同入夫妻宫，配偶性格随和';
    marriageAnalysis.relationship = '感情稳定，生活和谐';
    marriageAnalysis.timing = '27-32岁结婚较好';
    marriageAnalysis.partner = '配偶乐观开朗，有福气';
  } else if (majorStars.includes('贪狼')) {
    marriageAnalysis.spouseStars = '贪狼入夫妻宫，配偶有魅力但多桃花';
    marriageAnalysis.relationship = '感情浓烈但需要包容';
    marriageAnalysis.timing = '不宜过早结婚，30岁后较稳定';
    marriageAnalysis.partner = '配偶外貌出众，社交能力强';
  } else if (majorStars.includes('廉贞')) {
    marriageAnalysis.spouseStars = '廉贞入夫妻宫，配偶性格较强';
    marriageAnalysis.relationship = '感情需要磨合，但深情专一';
    marriageAnalysis.timing = '28-33岁结婚较合适';
    marriageAnalysis.partner = '配偶有个性，需要理解';
  } else {
    marriageAnalysis.spouseStars = '夫妻宫无主星，姻缘需要等待';
    marriageAnalysis.relationship = '感情发展较慢';
    marriageAnalysis.timing = '30岁后才有稳定姻缘';
    marriageAnalysis.partner = '需要通过介绍认识';
  }

  // 根据辅星调整
  if (auxiliaryStars.includes('红鸾') || auxiliaryStars.includes('天喜')) {
    marriageAnalysis.timing = '近期有婚姻机会，宜把握';
  }

  // 根据煞星调整
  if (maleficStars.length > 0) {
    marriageAnalysis.advice = '夫妻宫有煞星，感情需要用心经营，多沟通理解';
  } else {
    marriageAnalysis.advice = '夫妻宫配置良好，婚姻运势不错';
  }

  return marriageAnalysis;
}

// 紫微斗数综合分析生成器
function generateZiweiAnalysis(question, ziweiData, analysis, isMale = true) {
  const questionType = identifyQuestionType(question);
  const chart = ziweiData.chart;

  console.log('🔍 紫微分析 - 问题:', question);
  console.log('🔍 紫微分析 - 识别类型:', questionType);

  let specificAnalysis = {};

  switch (questionType) {
    case '财运':
      console.log('💰 执行财运分析...');
      specificAnalysis = analyzeZiweiWealth(chart, analysis, question);
      console.log('💰 财运分析结果:', specificAnalysis);
      break;
    case '事业':
      specificAnalysis = analyzeZiweiCareer(chart, analysis, question);
      break;
    case '婚姻':
    case '桃花':
      specificAnalysis = analyzeZiweiMarriage(chart, analysis, isMale, question);
      break;
    default:
      specificAnalysis = {
        focus: '综合命盘分析',
        advice: '根据紫微斗数命盘综合判断'
      };
  }

  return {
    questionType: questionType,
    specificAnalysis: specificAnalysis,
    mingGongStars: chart['命宫'].stars.map(s => s.name),
    overallScore: analysis.overallScore.score,
    patterns: analysis.patterns.map(p => p.name)
  };
}

module.exports = {
  generateZiweiAnalysis,
  generateBaziAnalysis,
  identifyQuestionType,
  analyzeZiweiWealth,
  analyzeZiweiCareer,
  analyzeZiweiMarriage,
  analyzeBaziWealth,
  analyzeBaziCareer,
  analyzeBaziMarriage,
  analyzeWealth,
  analyzeEducation,
  analyzeCareer,
  analyzeMarriage,
  analyzeHealth,
  analyzePartnership,
  generateCustomAnalysis
};
