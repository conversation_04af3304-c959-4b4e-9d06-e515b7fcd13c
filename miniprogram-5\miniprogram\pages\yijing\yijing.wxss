/* pages/yijing/yijing.wxss - 周易卦象（六爻）页面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.yijing-container {
  min-height: 100vh;
  padding: 40rpx 30rpx;
  font-family: 'STSong', '华文宋体', serif;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  font-size: 48rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.header-subtitle {
  font-size: 28rpx;
  color: var(--ink-gray);
  font-weight: 400;
}

/* 通用区块样式 */
.question-section,
.method-section {
  background: var(--paper-white);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.section-title {
  color: var(--ink-black);
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  letter-spacing: 1rpx;
}

.method-desc {
  padding: 24rpx;
  background: var(--ancient-paper);
  border-radius: 12rpx;
  border-left: 4rpx solid var(--ancient-gold);
}

.desc-text {
  color: var(--ink-black);
  font-size: 26rpx;
  line-height: 1.6;
}

/* 输入提示 */
.input-tip {
  color: var(--ink-gray);
  font-size: 24rpx;
  margin-top: 16rpx;
  text-align: center;
  line-height: 1.4;
}

/* 起卦方式选择 */
.method-selection {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.method-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.method-item {
  background: var(--ancient-paper);
  border-radius: 12rpx;
  padding: 25rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.method-item.active {
  background: linear-gradient(135deg, var(--ink-black), var(--ink-gray));
  border-color: var(--ancient-gold);
  color: var(--paper-white);
}

.method-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(26, 26, 26, 0.1);
}

.method-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.method-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: inherit;
}

.method-item .method-desc {
  font-size: 24rpx;
  color: var(--ink-gray);
  line-height: 1.4;
  padding: 0;
  background: none;
  border: none;
}

.method-item.active .method-desc {
  color: var(--ancient-paper);
}

/* 起卦参数输入 */
.params-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.hexagram-selector {
  margin-top: 20rpx;
}

.picker-display {
  background: var(--ancient-paper);
  border-radius: 8rpx;
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: var(--ink-black);
  border: 2rpx solid var(--ancient-gold);
}

/* 数字起卦和字数起卦区域 */
.number-section, .text-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.number-input, .text-input {
  background: var(--ancient-paper);
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: var(--ink-black);
  border: 2rpx solid var(--ancient-gold);
  margin-top: 20rpx;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 40rpx;
}



/* 投币过程 */
.throwing-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.throwing-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.throwing-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 12rpx;
}

.throwing-progress {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 500;
}

.coin-animation {
  text-align: center;
  margin-bottom: 40rpx;
}

/* 投币结果 */
.coin-results {
  margin-top: 40rpx;
}

.results-title {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 24rpx;
  text-align: center;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.result-item {
  background: var(--ancient-paper);
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 4rpx solid var(--ink-light);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-yao {
  font-size: 26rpx;
  color: var(--ink-black);
  font-weight: 600;
}

.result-type {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 500;
}

.result-coins {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.coin {
  background: var(--ink-light);
  color: var(--paper-white);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.coin-total {
  font-size: 22rpx;
  color: var(--ink-gray);
  margin-left: 8rpx;
}

.result-symbol {
  font-size: 32rpx;
  color: var(--ink-black);
  font-family: 'Courier New', monospace;
  text-align: center;
  font-weight: 600;
}

/* 分析中状态 */
.analyzing-section {
  text-align: center;
  padding: 80rpx 40rpx;
}

/* 卦象显示 */
.hexagram-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.hexagram-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.hexagram-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 12rpx;
}

.hexagram-question {
  font-size: 26rpx;
  color: var(--ancient-gold);
  margin-bottom: 8rpx;
  font-weight: 500;
}

.hexagram-time {
  font-size: 24rpx;
  color: var(--ink-gray);
}

/* 六爻显示 */
.hexagram-display {
  text-align: center;
}

.yaos-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  background: var(--ancient-paper);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 2rpx solid var(--ink-light);
}

.yao-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16rpx;
  background: var(--paper-white);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(26, 26, 26, 0.04);
}

.yao-position {
  font-size: 24rpx;
  color: var(--ink-gray);
  width: 80rpx;
  text-align: center;
  font-weight: 500;
}

.yao-symbol {
  flex: 1;
  font-size: 36rpx;
  color: var(--ink-black);
  font-family: 'Courier New', monospace;
  text-align: center;
  font-weight: 600;
  letter-spacing: 4rpx;
}

.yao-symbol.changing {
  color: var(--ancient-gold);
  animation: yaoGlow 2s ease-in-out infinite;
}

@keyframes yaoGlow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.yao-type {
  font-size: 22rpx;
  color: var(--ink-gray);
  width: 120rpx;
  text-align: center;
}

/* 动爻提示 */
.changing-yaos {
  text-align: center;
  margin-top: 24rpx;
  padding: 16rpx;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 12rpx;
}

.changing-text {
  font-size: 26rpx;
  color: var(--ancient-gold);
  font-weight: 600;
}

/* 分析结果 */
.analysis-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.analysis-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.analysis-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  text-align: center;
}

.analysis-content {
  line-height: 1.8;
}

.analysis-text {
  font-size: 28rpx;
  color: var(--ink-black);
  white-space: pre-line;
}

/* 底部操作按钮 */
.bottom-actions {
  margin-top: 40rpx;
}

/* 修复左下角黑圈问题 */
.yijing-container {
  position: relative;
  overflow: hidden;
}

.yijing-container::before,
.yijing-container::after {
  display: none !important;
}

/* 确保手工指定选择器可点击 */
.manual-section .picker-display {
  background: var(--ancient-paper);
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: var(--ink-black);
  border: 2rpx solid var(--ancient-gold);
  margin-top: 20rpx;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 40rpx;
  text-align: center;
  cursor: pointer;
}

/* 强制隐藏任何可能的黑色元素 */
view[style*="background: black"],
view[style*="background:black"],
view[style*="background-color: black"],
view[style*="background-color:black"],
view[style*="background: #000"],
view[style*="background:#000"],
view[style*="background-color: #000"],
view[style*="background-color:#000"] {
  display: none !important;
}

/* 隐藏可能的调试元素和浮动元素 */
.debug-panel,
.debug-button,
.floating-button,
.float-button,
[class*="debug"],
[class*="float"],
[class*="fixed"],
[id*="debug"],
[id*="float"] {
  display: none !important;
}

/* 隐藏所有可能的黑色形状元素（排除按钮组件） */
view[style*="border-radius: 50%"]:not(.ink-button),
view[style*="border-radius:50%"]:not(.ink-button),
view[style*="border-radius: 8px"]:not(.ink-button),
view[style*="border-radius:8px"]:not(.ink-button),
.circle:not(.ink-button),
.square:not(.ink-button) {
  display: none !important;
}

/* 隐藏微信开发者工具可能产生的元素 */
[class*="wx-"],
[id*="wx-"],
[class*="vconsole"],
[id*="vconsole"] {
  display: none !important;
}