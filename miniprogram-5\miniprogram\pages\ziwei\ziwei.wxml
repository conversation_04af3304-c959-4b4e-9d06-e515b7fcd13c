<!--pages/ziwei/ziwei.wxml - 紫微斗数页面-->
<view class="ziwei-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">紫微斗数</text>
    <text class="page-subtitle">陈希夷著·星宿命盘</text>
  </view>

  <!-- 输入区域 -->
  <view class="input-section" wx:if="{{!showResult}}">
    <!-- 问题输入 -->
    <view class="input-group">
      <text class="input-label">请输入您要问的问题</text>
      <textarea
        class="question-input"
        placeholder="例如：我的财运如何？何时能升职？感情运势怎样？"
        value="{{question}}"
        bindinput="onQuestionInput"
        maxlength="200"
      />
    </view>

    <!-- 出生信息 -->
    <view class="birth-info">
      <text class="section-title">出生信息</text>

      <!-- 性别选择 -->
      <view class="input-group">
        <text class="input-label">性别</text>
        <radio-group bindchange="onGenderChange">
          <label class="radio-item">
            <radio value="男" checked="{{isMale}}" />
            <text>男</text>
          </label>
          <label class="radio-item">
            <radio value="女" checked="{{!isMale}}" />
            <text>女</text>
          </label>
        </radio-group>
      </view>

      <!-- 出生日期 -->
      <view class="input-group">
        <text class="input-label">出生日期</text>
        <picker
          mode="date"
          value="{{birthDate}}"
          bindchange="onDateChange"
          class="date-picker"
        >
          <view class="picker-display">{{birthDate || '请选择日期'}}</view>
        </picker>
      </view>

      <!-- 出生时间 -->
      <view class="input-group">
        <text class="input-label">出生时间</text>
        <picker
          mode="time"
          value="{{birthTime}}"
          bindchange="onTimeChange"
          class="time-picker"
        >
          <view class="picker-display">{{birthTime || '请选择时间'}}</view>
        </picker>
      </view>

      <!-- 出生地 -->
      <view class="input-group">
        <text class="input-label">出生地</text>
        <picker range="{{cityList}}" range-key="name" value="{{selectedCityIndex}}" bindchange="onCityChange">
          <view class="picker-display">{{selectedCity || '请选择出生地'}}</view>
        </picker>
      </view>

      <!-- 真太阳时信息 -->
      <view class="input-group" wx:if="{{useTrueSolarTime}}">
        <view class="solar-time-info">
          <text class="solar-time-title">真太阳时修正</text>
          <text class="solar-time-desc">{{solarTimeExplanation}}</text>
          <text class="solar-time-result">修正后时间：{{trueSolarTimeString}}</text>
        </view>
      </view>
    </view>

    <!-- 开始排盘按钮 -->
    <view class="action-section">
      <button
        class="start-btn {{isAnalyzing ? 'analyzing' : ''}}"
        bindtap="onStartAnalysis"
        disabled="{{isAnalyzing}}"
      >
        {{isAnalyzing ? '正在排盘...' : '开始排盘'}}
      </button>
    </view>
  </view>

  <!-- 结果显示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <!-- 问题回顾 -->
    <view class="question-review">
      <text class="question-text">问题：{{question}}</text>
    </view>

    <!-- 紫微命盘 -->
    <view class="ziwei-chart">
      <text class="chart-title">紫微命盘</text>
      <view class="chart-grid">
        <!-- 十二宫位排列 -->
        <view
          wx:for="{{palaceList}}"
          wx:key="*this"
          class="palace-item {{getPalaceClass(item)}}"
        >
          <view class="palace-name">{{item}}</view>
          <view class="palace-branch">{{formattedChart[item].branch}}</view>
          <view class="palace-stars">{{formatPalaceStars(formattedChart[item])}}</view>
          <view wx:if="{{formattedChart[item].isMingGong}}" class="palace-mark">命</view>
          <view wx:if="{{formattedChart[item].isShenGong}}" class="palace-mark">身</view>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="basic-info">
      <text class="info-title">基本信息</text>
      <view class="info-item">
        <text class="info-label">命宫：</text>
        <text class="info-value">{{formattedChart['命宫'].branch}}宫</text>
      </view>
      <view class="info-item">
        <text class="info-label">五行局：</text>
        <text class="info-value">{{ziweiData.wuxingJu}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">命格：</text>
        <text class="info-value">{{analysis.mingGong.nature}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">综合评分：</text>
        <text class="info-value score">{{analysis.overallScore.score}}分 ({{analysis.overallScore.level}})</text>
      </view>
    </view>

    <!-- 格局分析 -->
    <view class="pattern-analysis" wx:if="{{analysis.patterns.length > 0}}">
      <text class="analysis-title">格局分析</text>
      <view
        wx:for="{{analysis.patterns}}"
        wx:key="name"
        class="pattern-item"
      >
        <text class="pattern-name">{{item.name}}</text>
        <text class="pattern-level">({{item.level}})</text>
        <text class="pattern-meaning">{{item.meaning}}</text>
      </view>
    </view>

    <!-- 精准问题分析 -->
    <view class="custom-analysis" wx:if="{{customAnalysis}}">
      <text class="analysis-title">精准分析 - {{customAnalysis.questionType || '综合'}}问题</text>
      <view class="analysis-content">
        <text class="analysis-text">{{formatZiweiAnalysis(customAnalysis)}}</text>
      </view>
    </view>

    <!-- AI智能分析 -->
    <view class="ai-analysis" wx:if="{{analysis.aiAnalysis}}">
      <text class="analysis-title">AI智能解读</text>
      <view class="analysis-content">
        <text class="ai-result">{{analysis.aiAnalysis}}</text>
      </view>
    </view>

    <!-- 综合总结 -->
    <view class="summary">
      <text class="summary-title">综合总结</text>
      <text class="summary-text">{{analysis.summary}}</text>
    </view>

    <!-- 重新排盘按钮 -->
    <view class="action-section">
      <button class="restart-btn" bindtap="onRestart">重新排盘</button>
    </view>
  </view>

  <!-- 预分析对话界面 -->
  <view class="conversation-panel" wx:if="{{showConversationPanel}}">
    <view class="conversation-header">
      <text class="conversation-title">精准度提升咨询</text>
      <text class="conversation-subtitle">为了提供更准确的分析，请回答以下问题</text>
      <view class="close-btn" bindtap="closeConversationMode">×</view>
    </view>

    <view class="conversation-content">
      <!-- 对话历史 -->
      <scroll-view class="conversation-history" scroll-y="true" scroll-top="{{scrollTop}}">
        <view class="message-item" wx:for="{{conversationHistory}}" wx:key="timestamp">
          <view class="message {{item.type === 'ai' ? 'ai-message' : 'user-message'}}">
            <view class="message-content">{{item.content}}</view>
            <view class="message-time">{{item.timestamp}}</view>
          </view>
        </view>

        <!-- AI正在打字指示器 -->
        <view class="typing-indicator" wx:if="{{isTyping}}">
          <view class="typing-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <text class="typing-text">AI正在思考...</text>
        </view>
      </scroll-view>

      <!-- 输入区域 -->
      <view class="conversation-input-area" wx:if="{{isWaitingResponse}}">
        <view class="input-container">
          <textarea
            class="conversation-input"
            placeholder="请输入您的回答..."
            value="{{conversationInput}}"
            bindinput="onConversationInput"
            maxlength="500"
          ></textarea>
          <button class="send-btn" bindtap="handleUserResponse">发送</button>
        </view>
      </view>
    </view>
  </view>
</view>