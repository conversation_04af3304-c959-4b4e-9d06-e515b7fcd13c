// 智能追问系统
// 基于分析结果智能生成追问问题，深入了解用户情况

const { conversationManager } = require('./conversation-manager');

/**
 * 智能追问系统类
 * 负责分析用户问题和分析结果，生成个性化的追问问题
 */
class IntelligentInquiry {
  constructor() {
    this.questionPriority = {
      'high': ['timing', 'method', 'obstacle'],
      'medium': ['alternative', 'resources', 'environment'],
      'low': ['personality', 'background', 'preference']
    };
  }

  /**
   * 基于分析结果生成预分析询问（分析前收集信息）
   * @param {string} question - 用户问题
   * @param {object} analysisData - 分析数据（卦象/八字/紫微等）
   * @param {string} moduleType - 模块类型
   * @param {string} sessionId - 会话ID
   * @returns {array} 预分析询问问题列表
   */
  async generatePreAnalysisQuestions(question, analysisData, moduleType, sessionId) {
    console.log(`🤖 为会话 ${sessionId} 生成预分析询问问题...`);

    // 分析问题类型和用户意图
    const questionAnalysis = this.analyzeUserQuestion(question);

    // 分析数据信息
    const dataAnalysis = this.analyzeData(analysisData, moduleType);

    // 基于知识库生成预分析问题
    const questions = await this.generateKnowledgeBasedQuestions(
      questionAnalysis,
      dataAnalysis,
      moduleType,
      analysisData
    );

    // 按重要性排序
    const prioritizedQuestions = this.prioritizePreAnalysisQuestions(questions, questionAnalysis);

    console.log(`✅ 生成了 ${prioritizedQuestions.length} 个预分析询问问题`);
    return prioritizedQuestions;
  },

  /**
   * 分析用户问题类型和意图
   * @param {string} question - 用户问题
   * @returns {object} 问题分析结果
   */
  analyzeUserQuestion(question) {
    const analysis = {
      type: 'general',
      intent: [],
      keywords: [],
      urgency: 'medium',
      specificity: 'general'
    };

    // 问题类型识别
    const typePatterns = {
      'career': ['工作', '事业', '职业', '升职', '跳槽', '创业', '生意'],
      'finance': ['财运', '赚钱', '投资', '理财', '收入', '财富', '经济'],
      'relationship': ['感情', '恋爱', '婚姻', '配偶', '对象', '桃花', '分手'],
      'health': ['健康', '身体', '疾病', '病情', '康复', '养生'],
      'family': ['家庭', '父母', '子女', '家人', '亲情'],
      'education': ['学习', '考试', '学业', '升学', '读书'],
      'timing': ['什么时候', '何时', '时间', '日期', '期限']
    };

    for (const [type, keywords] of Object.entries(typePatterns)) {
      if (keywords.some(keyword => question.includes(keyword))) {
        analysis.type = type;
        analysis.keywords.push(...keywords.filter(k => question.includes(k)));
      }
    }

    // 意图识别
    const intentPatterns = {
      'prediction': ['会', '能', '是否', '会不会', '能不能'],
      'timing': ['什么时候', '何时', '多久', '期限'],
      'method': ['怎么', '如何', '怎样', '方法', '建议'],
      'choice': ['选择', '决定', '哪个', '还是', '或者']
    };

    for (const [intent, patterns] of Object.entries(intentPatterns)) {
      if (patterns.some(pattern => question.includes(pattern))) {
        analysis.intent.push(intent);
      }
    }

    // 紧急程度判断
    const urgencyKeywords = {
      'high': ['急', '紧急', '马上', '立即', '现在', '今天'],
      'low': ['将来', '以后', '未来', '长期', '慢慢']
    };

    for (const [level, keywords] of Object.entries(urgencyKeywords)) {
      if (keywords.some(keyword => question.includes(keyword))) {
        analysis.urgency = level;
        break;
      }
    }

    return analysis;
  },

  /**
   * 分析数据信息（卦象/八字/紫微等）
   * @param {object} analysisData - 分析数据
   * @param {string} moduleType - 模块类型
   * @returns {object} 数据分析结果
   */
  analyzeData(analysisData, moduleType) {
    const analysis = {
      dataType: moduleType,
      keyElements: [],
      traditionalMeaning: '',
      applicableScenarios: []
    };

    // 根据模块类型分析数据
    switch (moduleType) {
      case 'yijing':
        analysis.keyElements = this.extractLiuyaoElements(analysisData);
        analysis.traditionalMeaning = this.getLiuyaoTraditionalMeaning(analysisData.name);
        break;
      case 'meihua':
        analysis.keyElements = this.extractMeihuaElements(analysisData);
        analysis.traditionalMeaning = this.getMeihuaTraditionalMeaning(analysisData.name);
        break;
      case 'bazi':
        analysis.keyElements = this.extractBaziElements(analysisData);
        analysis.traditionalMeaning = '子平八字命理分析';
        break;
      case 'ziwei':
        analysis.keyElements = this.extractZiweiElements(analysisData);
        analysis.traditionalMeaning = '紫微斗数命盘分析';
        break;
    }

    return analysis;
  },

  /**
   * 提取六爻关键要素
   */
  extractLiuyaoElements(hexagramInfo) {
    const elements = [];

    if (hexagramInfo.changingYaos && hexagramInfo.changingYaos.length > 0) {
      elements.push(`动爻：第${hexagramInfo.changingYaos.join('、')}爻`);
    }

    if (hexagramInfo.liuyaoInfo) {
      if (hexagramInfo.liuyaoInfo.worldResponse) {
        elements.push(`世应：${hexagramInfo.liuyaoInfo.worldResponse}`);
      }
      if (hexagramInfo.liuyaoInfo.sixRelatives) {
        elements.push(`六亲：${hexagramInfo.liuyaoInfo.sixRelatives.join('、')}`);
      }
    }

    return elements;
  },

  /**
   * 提取梅花易数关键要素
   */
  extractMeihuaElements(hexagramInfo) {
    const elements = [];

    if (hexagramInfo.upper && hexagramInfo.lower) {
      elements.push(`体卦：${hexagramInfo.lower.name}`);
      elements.push(`用卦：${hexagramInfo.upper.name}`);
    }

    if (hexagramInfo.changingLine) {
      elements.push(`动爻：第${hexagramInfo.changingLine}爻`);
    }

    return elements;
  },

  /**
   * 获取六爻传统含义（基于知识库）
   */
  getLiuyaoTraditionalMeaning(hexagramName) {
    // 基于知识库的传统卦象含义
    const traditionalMeanings = {
      '乾为天': '刚健中正，君子自强不息，利于领导和创业',
      '坤为地': '柔顺承载，厚德载物，利于合作和辅助',
      '水雷屯': '初始困难，需要积累，不宜急进',
      '山水蒙': '启蒙教育，需要指导，宜求师问道',
      '水天需': '等待时机，需要耐心，不可强求',
      '天水讼': '争讼纠纷，需要谨慎，宜和解',
      '地水师': '统帅军队，需要纪律，宜团结',
      '水地比': '亲密合作，需要诚信，宜结盟'
    };

    return traditionalMeanings[hexagramName] || '需要根据具体情况分析';
  },

  /**
   * 获取梅花易数传统含义
   */
  getMeihuaTraditionalMeaning(hexagramName) {
    // 基于知识库的梅花易数含义
    const meihuaMeanings = {
      '乾为天': '天行健，君子以自强不息，主动积极',
      '坤为地': '地势坤，君子以厚德载物，柔顺包容',
      '震为雷': '雷声震动，主动变化，宜把握时机',
      '巽为风': '风行天下，渐进发展，宜顺势而为',
      '坎为水': '水流不息，险中求进，需要智慧',
      '离为火': '火光明亮，文明进步，宜展现才华',
      '艮为山': '山止不动，稳重踏实，宜守正待时',
      '兑为泽': '泽润万物，和悦交流，宜人际和谐'
    };

    return meihuaMeanings[hexagramName] || '需要结合体用关系分析';
  },

  /**
   * 提取八字关键要素
   */
  extractBaziElements(baziData) {
    const elements = [];

    if (baziData.dayMaster) {
      elements.push(`日主：${baziData.dayMaster}`);
    }
    if (baziData.dayMasterElement) {
      elements.push(`五行：${baziData.dayMasterElement}`);
    }
    if (baziData.pattern) {
      elements.push(`格局：${baziData.pattern}`);
    }
    if (baziData.favorableElements) {
      elements.push(`用神：${baziData.favorableElements.join('、')}`);
    }

    return elements;
  },

  /**
   * 提取紫微斗数关键要素
   */
  extractZiweiElements(ziweiData) {
    const elements = [];

    if (ziweiData.mainStar) {
      elements.push(`命宫主星：${ziweiData.mainStar}`);
    }
    if (ziweiData.wealthPalace && ziweiData.wealthPalace.length > 0) {
      elements.push(`财帛宫：${ziweiData.wealthPalace.join('、')}`);
    }
    if (ziweiData.careerPalace && ziweiData.careerPalace.length > 0) {
      elements.push(`官禄宫：${ziweiData.careerPalace.join('、')}`);
    }
    if (ziweiData.marriagePalace && ziweiData.marriagePalace.length > 0) {
      elements.push(`夫妻宫：${ziweiData.marriagePalace.join('、')}`);
    }

    return elements;
  },

  /**
   * 基于知识库生成预分析问题
   * @param {object} questionAnalysis - 问题分析
   * @param {object} hexagramAnalysis - 卦象分析
   * @param {string} moduleType - 模块类型
   * @param {object} context - 会话上下文
   * @returns {array} 基于知识库的问题列表
   */
  async generateKnowledgeBasedQuestions(questionAnalysis, hexagramAnalysis, moduleType, context) {
    const questions = [];

    // 基于问题类型生成知识库相关问题
    const typeBasedQuestions = this.getKnowledgeBasedTypeQuestions(questionAnalysis.type, moduleType);
    questions.push(...typeBasedQuestions);

    // 基于命盘特性生成问题（卦象、八字、紫微等）
    const chartBasedQuestions = this.getChartBasedQuestions(hexagramAnalysis, questionAnalysis.type, moduleType);
    questions.push(...chartBasedQuestions);

    // 基于传统理论生成问题
    const theoryBasedQuestions = this.getTheoryBasedQuestions(moduleType, questionAnalysis);
    questions.push(...theoryBasedQuestions);

    return questions.slice(0, 4); // 限制最多4个问题
  },

  /**
   * 分析占卜结果中的关键信息
   * @param {object} result - 分析结果
   * @param {string} moduleType - 模块类型
   * @returns {object} 结果分析
   */
  analyzeResult(result, moduleType) {
    const analysis = {
      sentiment: 'neutral', // positive, negative, neutral
      confidence: 'medium',
      keyPoints: [],
      suggestions: [],
      warnings: [],
      opportunities: []
    };

    const resultText = result.aiAnalysis || result.analysis || '';

    // 情感倾向分析
    const positiveWords = ['吉', '好', '顺利', '成功', '有利', '旺', '佳', '利'];
    const negativeWords = ['凶', '不利', '困难', '阻碍', '破', '冲', '害', '忌'];

    const positiveCount = positiveWords.filter(word => resultText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => resultText.includes(word)).length;

    if (positiveCount > negativeCount) {
      analysis.sentiment = 'positive';
    } else if (negativeCount > positiveCount) {
      analysis.sentiment = 'negative';
    }

    // 提取关键建议
    const suggestionPatterns = ['建议', '应该', '可以', '不宜', '适合', '避免'];
    suggestionPatterns.forEach(pattern => {
      const regex = new RegExp(`${pattern}[^。！？]*[。！？]`, 'g');
      const matches = resultText.match(regex);
      if (matches) {
        analysis.suggestions.push(...matches);
      }
    });

    // 提取时间信息
    const timePatterns = ['月', '年', '日', '时间', '期间', '阶段'];
    timePatterns.forEach(pattern => {
      if (resultText.includes(pattern)) {
        analysis.keyPoints.push(`包含时间预测：${pattern}`);
      }
    });

    return analysis;
  },

  /**
   * 基于知识库生成问题类型相关问题
   */
  getKnowledgeBasedTypeQuestions(questionType, moduleType) {
    const knowledgeBasedQuestions = {
      yijing: {
        career: [
          { text: '您目前从事什么行业？这有助于分析官鬼爻的具体含义。', priority: 'high', knowledge: '官鬼爻代表工作事业，不同行业有不同解读' },
          { text: '您是想升职、跳槽，还是创业？不同目标对应不同的六亲分析。', priority: 'high', knowledge: '升职看官鬼，跳槽看世应，创业看财爻' },
          { text: '您的年龄段是？青年、中年、老年在六爻中有不同的判断标准。', priority: 'medium', knowledge: '年龄影响大运流年的解读' }
        ],
        finance: [
          { text: '您是想了解正财（工资收入）还是偏财（投资收益）？', priority: 'high', knowledge: '妻财爻分正财偏财，解读方法不同' },
          { text: '您的投资类型是什么？股票、房产、生意？不同类型对应不同的五行分析。', priority: 'high', knowledge: '五行生克决定投资成败' },
          { text: '您希望了解短期（1年内）还是长期（3-5年）的财运？', priority: 'medium', knowledge: '时间长短影响动静爻的解读' }
        ],
        relationship: [
          { text: '您是男性还是女性？这决定了如何看配偶宫。', priority: 'high', knowledge: '男看妻财女看官鬼，性别决定六亲取用' },
          { text: '您目前是单身、恋爱中还是已婚？不同状态的分析重点不同。', priority: 'high', knowledge: '单身看桃花，恋爱看合冲，已婚看刑害' },
          { text: '您的年龄段？不同年龄的感情问题有不同的解读方式。', priority: 'medium', knowledge: '年龄影响感情运势的判断' }
        ]
      },
      meihua: {
        career: [
          { text: '您希望了解事业的哪个方面？发展方向、时机选择还是人际关系？', priority: 'high', knowledge: '体用生克决定事业成败' },
          { text: '您的性格偏向主动还是被动？这影响体用关系的解读。', priority: 'medium', knowledge: '性格与卦象相应，影响成事概率' }
        ],
        finance: [
          { text: '您的理财风格是稳健还是激进？这与五行属性相关。', priority: 'high', knowledge: '五行属性决定适合的投资方式' },
          { text: '您最关心的是投资时机还是投资方向？', priority: 'high', knowledge: '时机看动爻，方向看体用' }
        ]
      },
      bazi: {
        career: [
          { text: '您目前的工作性质是什么？文职、武职、技术还是管理？这决定了如何分析官杀星。', priority: 'high', knowledge: '正官主文职管理，七杀主武职技术，不同工作性质解读不同' },
          { text: '您是希望在现有岗位发展还是寻求突破？这影响大运流年的分析重点。', priority: 'high', knowledge: '安于现状看正官，求变突破看七杀，大运配合很重要' },
          { text: '您的年龄段？青年（20-35）、中年（35-50）还是其他？不同年龄段的事业重点不同。', priority: 'medium', knowledge: '青年重立业，中年重发展，老年重传承，各有不同的大运配合' }
        ],
        finance: [
          { text: '您是想了解正财（稳定收入）还是偏财（投资收益）？两者的分析方法完全不同。', priority: 'high', knowledge: '正财主稳定收入工资，偏财主投资收益，喜忌不同' },
          { text: '您的理财方式偏保守还是激进？这与您的财星喜忌有关。', priority: 'high', knowledge: '财星为喜神宜激进，为忌神宜保守，需要结合八字分析' },
          { text: '您最关心哪个时间段的财运？近1-2年还是未来5-10年？', priority: 'medium', knowledge: '近期看流年，长期看大运，时间不同分析重点不同' }
        ],
        relationship: [
          { text: '您是男性还是女性？这决定了如何看配偶星。', priority: 'high', knowledge: '男命以财星为妻，女命以官星为夫，性别决定取用神' },
          { text: '您目前的感情状态？单身、恋爱、已婚还是其他？', priority: 'high', knowledge: '不同感情状态的分析重点不同，单身看桃花，已婚看夫妻宫' },
          { text: '您希望了解感情的哪个方面？缘分时机、对象特征还是婚姻稳定性？', priority: 'medium', knowledge: '缘分看桃花，特征看配偶星，稳定性看夫妻宫刑冲' }
        ],
        health: [
          { text: '您或家人有什么慢性疾病需要特别关注吗？', priority: 'high', knowledge: '八字可以看出先天体质和易患疾病，需要结合实际情况' },
          { text: '您最关心身体的哪个部位？心脏、肝脏、脾胃还是其他？', priority: 'medium', knowledge: '五行对应五脏，木主肝胆，火主心脏，土主脾胃，金主肺，水主肾' }
        ],
        family: [
          { text: '您想了解与父母、子女还是兄弟姐妹的关系？', priority: 'high', knowledge: '印星看父母，食伤看子女，比劫看兄弟，各有不同分析方法' },
          { text: '您在家庭中的角色是什么？长子、次子还是独生子女？', priority: 'medium', knowledge: '家庭排行影响六亲关系的解读' }
        ]
      },
      ziwei: {
        career: [
          { text: '您的性格偏向领导型、技术型还是服务型？这与主星特质相关。', priority: 'high', knowledge: '紫微主领导，天机主技术，天同主服务，不同主星适合不同职业' },
          { text: '您希望在什么环境下工作？大企业、小公司还是自主创业？', priority: 'high', knowledge: '官禄宫主星决定适合的工作环境和发展方向' },
          { text: '您最看重事业的哪个方面？权力地位、技术专精还是人际和谐？', priority: 'medium', knowledge: '不同主星有不同的事业追求和发展重点' }
        ],
        finance: [
          { text: '您的消费习惯是节俭还是大方？这与财帛宫星曜有关。', priority: 'high', knowledge: '财帛宫不同星曜决定理财方式，武曲主节俭，贪狼主消费' },
          { text: '您倾向于稳定投资还是高风险高收益？', priority: 'high', knowledge: '财帛宫星曜决定适合的投资方式和风险承受能力' },
          { text: '您最关心财富的积累还是现金流？', priority: 'medium', knowledge: '积累看财帛宫，现金流看田宅宫，重点不同' }
        ],
        relationship: [
          { text: '您在感情中偏主动还是被动？这与夫妻宫星曜特质相关。', priority: 'high', knowledge: '夫妻宫不同星曜决定感情模式，紫微主动，天同被动' },
          { text: '您希望找什么类型的伴侣？事业型、家庭型还是艺术型？', priority: 'high', knowledge: '夫妻宫星曜显示适合的配偶类型和相处模式' },
          { text: '您最看重感情的哪个方面？精神契合、物质基础还是外在条件？', priority: 'medium', knowledge: '不同星曜组合决定感情的重点和需求' }
        ],
        health: [
          { text: '您的作息规律如何？这与疾厄宫星曜有关。', priority: 'medium', knowledge: '疾厄宫星曜显示体质特点和健康注意事项' },
          { text: '您平时的运动习惯如何？', priority: 'low', knowledge: '运动习惯与体质相关，需要结合疾厄宫分析' }
        ],
        family: [
          { text: '您与父母的关系如何？亲密、一般还是疏远？', priority: 'high', knowledge: '父母宫星曜决定与父母的关系和互动模式' },
          { text: '您希望要几个孩子？这与子女宫星曜有关。', priority: 'medium', knowledge: '子女宫星曜显示子女缘分和教育方式' }
        ]
      }
    };

    const moduleQuestions = knowledgeBasedQuestions[moduleType] || {};
    return moduleQuestions[questionType] || [];
  },

  /**
   * 基于命盘特性生成问题（支持卦象、八字、紫微等）
   */
  getChartBasedQuestions(chartAnalysis, questionType, moduleType) {
    const questions = [];

    if (moduleType === 'yijing' || moduleType === 'meihua') {
      // 卦象相关问题
      return this.getHexagramSpecificQuestions(chartAnalysis, questionType);
    } else if (moduleType === 'bazi') {
      // 八字相关问题
      return this.getBaziSpecificQuestions(chartAnalysis, questionType);
    } else if (moduleType === 'ziwei') {
      // 紫微相关问题
      return this.getZiweiSpecificQuestions(chartAnalysis, questionType);
    }

    return questions;
  },

  /**
   * 基于卦象特性生成问题
   */
  getHexagramSpecificQuestions(hexagramAnalysis, questionType) {
    const questions = [];

    // 根据卦象名称生成特定问题
    const hexagramName = hexagramAnalysis.hexagramName;

    if (hexagramName.includes('乾')) {
      questions.push({
        text: '乾卦主刚健，您是否准备主动出击？还是希望稳妥行事？',
        priority: 'high',
        knowledge: '乾卦刚健，利于主动，但需要把握分寸'
      });
    } else if (hexagramName.includes('坤')) {
      questions.push({
        text: '坤卦主柔顺，您是否愿意采取配合、辅助的角色？',
        priority: 'high',
        knowledge: '坤卦柔顺，利于配合，不宜强出头'
      });
    } else if (hexagramName.includes('屯')) {
      questions.push({
        text: '屯卦主困难，您目前遇到的最大阻碍是什么？',
        priority: 'high',
        knowledge: '屯卦初难，需要了解具体困难才能给出建议'
      });
    }

    return questions;
  },

  /**
   * 基于传统理论生成问题
   */
  getTheoryBasedQuestions(moduleType, questionAnalysis) {
    const theoryQuestions = {
      yijing: [
        { text: '您希望了解具体的时间节点吗？比如几月份比较有利？', priority: 'medium', knowledge: '六爻可以预测具体时间，需要结合月令分析' },
        { text: '您有什么特别需要避免的事情吗？', priority: 'medium', knowledge: '忌神所在，需要特别注意' }
      ],
      meihua: [
        { text: '您做这件事的环境如何？是在家里、办公室还是其他地方？', priority: 'medium', knowledge: '梅花易数重视环境因素，影响卦象解读' },
        { text: '您的直觉告诉您这件事会如何发展？', priority: 'low', knowledge: '梅花易数重视直觉感应' }
      ]
    };

    return theoryQuestions[moduleType] || [];
  },

  /**
   * 预分析问题优先级排序
   */
  prioritizePreAnalysisQuestions(questions, questionAnalysis) {
    const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };

    return questions.sort((a, b) => {
      const aPriority = priorityOrder[a.priority] || 1;
      const bPriority = priorityOrder[b.priority] || 1;
      return bPriority - aPriority;
    });
  },

  /**
   * 生成个性化追问问题
   * @param {object} questionAnalysis - 问题分析
   * @param {object} resultAnalysis - 结果分析
   * @param {string} moduleType - 模块类型
   * @param {object} context - 会话上下文
   * @returns {array} 个性化问题列表
   */
  async generatePersonalizedQuestions(questionAnalysis, resultAnalysis, moduleType, context) {
    const questions = [];

    // 基于问题类型生成追问
    const typeBasedQuestions = this.getTypeBasedQuestions(questionAnalysis.type, moduleType);
    questions.push(...typeBasedQuestions);

    // 基于分析结果生成追问
    const resultBasedQuestions = this.getResultBasedQuestions(resultAnalysis, questionAnalysis);
    questions.push(...resultBasedQuestions);

    // 基于模块特性生成追问
    const moduleBasedQuestions = this.getModuleBasedQuestions(moduleType, questionAnalysis);
    questions.push(...moduleBasedQuestions);

    // 去重和个性化调整
    const uniqueQuestions = this.deduplicateQuestions(questions);
    const personalizedQuestions = this.personalizeQuestions(uniqueQuestions, context);

    return personalizedQuestions.slice(0, 5); // 限制最多5个问题
  },

  /**
   * 基于问题类型获取追问问题
   */
  getTypeBasedQuestions(type, moduleType) {
    const questionBank = {
      career: [
        { text: '您目前的工作状况如何？是否遇到了具体的困难？', priority: 'high', category: 'current_status' },
        { text: '您理想的职业发展方向是什么？', priority: 'medium', category: 'goal' },
        { text: '您在工作中最看重什么？薪资、发展空间还是工作环境？', priority: 'medium', category: 'preference' }
      ],
      finance: [
        { text: '您目前的财务状况如何？有什么具体的理财目标吗？', priority: 'high', category: 'current_status' },
        { text: '您的风险承受能力如何？偏好稳健还是激进的投资方式？', priority: 'high', category: 'preference' },
        { text: '您希望在多长时间内实现财务目标？', priority: 'medium', category: 'timeline' }
      ],
      relationship: [
        { text: '您目前的感情状况如何？单身还是有伴侣？', priority: 'high', category: 'current_status' },
        { text: '您理想的伴侣类型是什么样的？', priority: 'medium', category: 'preference' },
        { text: '您在感情中遇到的主要困扰是什么？', priority: 'high', category: 'obstacle' }
      ],
      health: [
        { text: '您目前的身体状况如何？有什么不适症状吗？', priority: 'high', category: 'current_status' },
        { text: '您的生活作息和饮食习惯怎样？', priority: 'medium', category: 'lifestyle' },
        { text: '您有定期体检或运动的习惯吗？', priority: 'low', category: 'habit' }
      ]
    };

    return questionBank[type] || [];
  },

  /**
   * 基于分析结果获取追问问题
   */
  getResultBasedQuestions(resultAnalysis, questionAnalysis) {
    const questions = [];

    // 基于情感倾向
    if (resultAnalysis.sentiment === 'negative') {
      questions.push({
        text: '看起来情况比较复杂，您最担心的是什么方面？',
        priority: 'high',
        category: 'concern'
      });
    } else if (resultAnalysis.sentiment === 'positive') {
      questions.push({
        text: '机会很好！您准备如何把握这个机会？',
        priority: 'medium',
        category: 'action'
      });
    }

    // 基于建议内容
    if (resultAnalysis.suggestions.length > 0) {
      questions.push({
        text: '关于刚才的建议，您觉得哪一点最适合您的情况？',
        priority: 'high',
        category: 'feedback'
      });
    }

    return questions;
  },

  /**
   * 基于模块特性获取追问问题
   */
  getModuleBasedQuestions(moduleType, questionAnalysis) {
    const moduleQuestions = {
      yijing: [
        { text: '您希望了解更具体的时间节点吗？', priority: 'high', category: 'timing' },
        { text: '您想知道应该采取什么具体行动吗？', priority: 'high', category: 'method' }
      ],
      meihua: [
        { text: '您当前的环境和条件如何？', priority: 'medium', category: 'environment' },
        { text: '您的性格特点是什么？这有助于给出更适合的建议。', priority: 'low', category: 'personality' }
      ],
      bazi: [
        { text: '您想了解未来几年的运势变化吗？', priority: 'high', category: 'fortune' },
        { text: '您最关心人生的哪个方面？事业、财运还是感情？', priority: 'medium', category: 'focus' }
      ],
      ziwei: [
        { text: '您的人生目标和理想是什么？', priority: 'medium', category: 'goal' },
        { text: '您最希望在哪个方面得到发展和提升？', priority: 'medium', category: 'development' }
      ]
    };

    return moduleQuestions[moduleType] || [];
  },

  /**
   * 问题去重
   */
  deduplicateQuestions(questions) {
    const seen = new Set();
    return questions.filter(q => {
      const key = q.text.substring(0, 20); // 使用前20个字符作为去重标识
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  },

  /**
   * 个性化问题调整
   */
  personalizeQuestions(questions, context) {
    // 根据用户历史和偏好调整问题
    return questions.map(q => ({
      ...q,
      id: `q_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`,
      timestamp: new Date()
    }));
  },

  /**
   * 问题优先级排序
   */
  prioritizeQuestions(questions, questionAnalysis) {
    const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
    
    return questions.sort((a, b) => {
      const aPriority = priorityOrder[a.priority] || 1;
      const bPriority = priorityOrder[b.priority] || 1;
      return bPriority - aPriority;
    });
  },

  /**
   * 生成AI驱动的深度追问
   * @param {string} sessionId - 会话ID
   * @param {string} userResponse - 用户回答
   * @returns {object} AI生成的追问
   */
  async generateAIFollowUp(sessionId, userResponse) {
    const session = conversationManager.getSession(sessionId);
    if (!session) return null;

    try {
      // 构建AI追问提示词
      const prompt = this.buildFollowUpPrompt(session, userResponse);
      
      // 调用DeepSeek API生成追问
      const aiResponse = await this.callDeepSeekForFollowUp(prompt);
      
      if (aiResponse.success) {
        return {
          type: 'ai_generated',
          text: aiResponse.question,
          context: aiResponse.context,
          priority: 'high'
        };
      }
      
    } catch (error) {
      console.error('AI追问生成失败:', error);
    }
    
    return null;
  },

  /**
   * 构建AI追问提示词
   */
  buildFollowUpPrompt(session, userResponse) {
    const { moduleType, context, messageHistory } = session;
    const lastAnalysis = context.analysisResults[context.analysisResults.length - 1];
    
    return `你是专业的${this.getModuleName(moduleType)}大师，正在与用户进行深入的占卜咨询。

【对话历史】
${messageHistory.slice(-3).map(m => `${m.role}: ${m.content}`).join('\n')}

【最新分析结果】
${lastAnalysis ? lastAnalysis.result.aiAnalysis : '暂无'}

【用户最新回答】
${userResponse}

请基于以上信息，生成一个深入的追问问题，要求：
1. 针对用户的具体情况
2. 有助于提供更精准的指导
3. 体现专业的${this.getModuleName(moduleType)}理论
4. 语气亲切专业，像面对面咨询

只返回追问问题，不要其他内容。`;
  },

  /**
   * 调用DeepSeek API生成追问
   */
  async callDeepSeekForFollowUp(prompt) {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://api.deepseek.com/v1/chat/completions',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-********************************'
        },
        data: {
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
          max_tokens: 200
        },
        success: (response) => {
          if (response.statusCode === 200 && response.data?.choices?.[0]) {
            resolve({
              success: true,
              question: response.data.choices[0].message.content.trim()
            });
          } else {
            resolve({ success: false, error: 'API响应异常' });
          }
        },
        fail: (error) => {
          resolve({ success: false, error: error.errMsg });
        }
      });
    });
  },

  /**
   * 基于八字特征生成问题
   */
  getBaziSpecificQuestions(baziAnalysis, questionType) {
    const questions = [];

    // 基于日主强弱生成问题
    if (baziAnalysis.dayMasterStrength === 'strong') {
      questions.push({
        text: '您的性格比较强势主动，在处理这个问题时是否考虑过他人的感受？',
        priority: 'high',
        knowledge: '日主强的人性格主动，但需要注意与他人的协调'
      });
    } else if (baziAnalysis.dayMasterStrength === 'weak') {
      questions.push({
        text: '您在面对困难时是否容易犹豫不决？需要他人的支持和帮助吗？',
        priority: 'high',
        knowledge: '日主弱的人需要帮扶，宜寻求贵人相助'
      });
    }

    // 基于用神生成问题
    if (baziAnalysis.favorableElements && baziAnalysis.favorableElements.includes('木')) {
      questions.push({
        text: '您是否适合在东方发展？或者从事与木相关的行业？',
        priority: 'medium',
        knowledge: '木为用神者，宜向东方发展，从事木相关行业'
      });
    }

    // 基于十神格局生成问题
    if (baziAnalysis.pattern && baziAnalysis.pattern.includes('正官')) {
      questions.push({
        text: '您是否希望在体制内发展？或者追求稳定的职业道路？',
        priority: 'high',
        knowledge: '正官格的人适合体制内发展，追求稳定'
      });
    } else if (baziAnalysis.pattern && baziAnalysis.pattern.includes('七杀')) {
      questions.push({
        text: '您是否喜欢挑战和竞争？愿意承担风险来获得更大的成功？',
        priority: 'high',
        knowledge: '七杀格的人喜欢挑战，适合竞争性强的环境'
      });
    }

    return questions;
  },

  /**
   * 基于紫微命盘特征生成问题
   */
  getZiweiSpecificQuestions(ziweiAnalysis, questionType) {
    const questions = [];

    // 基于命宫主星生成问题
    if (ziweiAnalysis.mainStar === '紫微') {
      questions.push({
        text: '您是否有很强的领导欲望？希望在团队中担任主导角色？',
        priority: 'high',
        knowledge: '紫微星主领导，有帝王之气，适合管理职位'
      });
    } else if (ziweiAnalysis.mainStar === '天机') {
      questions.push({
        text: '您是否喜欢思考和分析？对技术或策划类工作感兴趣？',
        priority: 'high',
        knowledge: '天机星主智慧，善于思考分析，适合技术策划'
      });
    } else if (ziweiAnalysis.mainStar === '太阳') {
      questions.push({
        text: '您是否性格开朗外向？喜欢帮助他人？',
        priority: 'high',
        knowledge: '太阳星主光明，性格开朗，乐于助人'
      });
    }

    // 基于财帛宫生成问题
    if (ziweiAnalysis.wealthPalace && ziweiAnalysis.wealthPalace.includes('武曲')) {
      questions.push({
        text: '您在理财方面是否比较谨慎？喜欢稳健的投资方式？',
        priority: 'medium',
        knowledge: '武曲在财帛宫主理财谨慎，适合稳健投资'
      });
    } else if (ziweiAnalysis.wealthPalace && ziweiAnalysis.wealthPalace.includes('贪狼')) {
      questions.push({
        text: '您是否对投资理财很有兴趣？愿意尝试多种赚钱方式？',
        priority: 'medium',
        knowledge: '贪狼在财帛宫主多元化赚钱，投资欲望强'
      });
    }

    return questions;
  },

  /**
   * 获取模块中文名称
   */
  getModuleName(moduleType) {
    const names = {
      'yijing': '六爻',
      'meihua': '梅花易数',
      'bazi': '八字',
      'ziwei': '紫微斗数'
    };
    return names[moduleType] || '占卜';
  }
}

// 创建全局实例
const intelligentInquiry = new IntelligentInquiry();

module.exports = {
  IntelligentInquiry,
  intelligentInquiry
};
