// 紫微斗数排盘计算系统
// 基于传统紫微斗数理论

// 天干地支数据
export const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
export const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 十二宫位
export const TWELVE_PALACES = [
  '命宫', '兄弟宫', '夫妻宫', '子女宫', '财帛宫', '疾厄宫',
  '迁移宫', '奴仆宫', '官禄宫', '田宅宫', '福德宫', '父母宫'
];

// 紫微斗数主星
export const MAJOR_STARS = {
  '紫微': { type: '甲级主星', brightness: 5, nature: '帝星' },
  '天机': { type: '甲级主星', brightness: 4, nature: '智星' },
  '太阳': { type: '甲级主星', brightness: 5, nature: '贵星' },
  '武曲': { type: '甲级主星', brightness: 4, nature: '财星' },
  '天同': { type: '甲级主星', brightness: 4, nature: '福星' },
  '廉贞': { type: '甲级主星', brightness: 4, nature: '囚星' },
  '天府': { type: '甲级主星', brightness: 5, nature: '库星' },
  '太阴': { type: '甲级主星', brightness: 4, nature: '富星' },
  '贪狼': { type: '甲级主星', brightness: 4, nature: '桃花星' },
  '巨门': { type: '甲级主星', brightness: 4, nature: '暗星' },
  '天相': { type: '甲级主星', brightness: 4, nature: '印星' },
  '天梁': { type: '甲级主星', brightness: 4, nature: '寿星' },
  '七杀': { type: '甲级主星', brightness: 4, nature: '将星' },
  '破军': { type: '甲级主星', brightness: 4, nature: '耗星' }
};

// 辅星
export const AUXILIARY_STARS = {
  '左辅': { type: '乙级辅星', brightness: 3 },
  '右弼': { type: '乙级辅星', brightness: 3 },
  '文昌': { type: '乙级辅星', brightness: 3 },
  '文曲': { type: '乙级辅星', brightness: 3 },
  '天魁': { type: '乙级辅星', brightness: 3 },
  '天钺': { type: '乙级辅星', brightness: 3 },
  '禄存': { type: '乙级辅星', brightness: 3 },
  '天马': { type: '乙级辅星', brightness: 3 }
};

// 煞星
export const MALEFIC_STARS = {
  '擎羊': { type: '丙级煞星', brightness: 2 },
  '陀罗': { type: '丙级煞星', brightness: 2 },
  '火星': { type: '丙级煞星', brightness: 2 },
  '铃星': { type: '丙级煞星', brightness: 2 },
  '地空': { type: '丙级煞星', brightness: 2 },
  '地劫': { type: '丙级煞星', brightness: 2 }
};

/**
 * 计算紫微斗数命盘
 * @param {Date} birthDate - 出生日期时间
 * @param {boolean} isMale - 是否为男性
 * @param {boolean} isLunar - 是否为农历（暂时简化处理）
 * @returns {Object} 紫微斗数命盘
 */
export function calculateZiweiChart(birthDate, isMale = true, isLunar = false) {
  const year = birthDate.getFullYear();
  const month = birthDate.getMonth() + 1;
  const day = birthDate.getDate();
  const hour = birthDate.getHours();
  
  // 计算农历信息（简化处理）
  const lunarInfo = calculateLunarInfo(year, month, day);
  
  // 计算命宫
  const mingGong = calculateMingGong(lunarInfo.month, hour);
  
  // 计算身宫
  const shenGong = calculateShenGong(lunarInfo.month, hour);
  
  // 计算五行局
  const wuxingJu = calculateWuxingJu(lunarInfo.year);

  // 安主星
  const majorStarsPlacement = placeMajorStars(lunarInfo, mingGong, wuxingJu);
  
  // 安辅星
  const auxiliaryStarsPlacement = placeAuxiliaryStars(lunarInfo, hour);
  
  // 安煞星
  const maleficStarsPlacement = placeMaleficStars(lunarInfo, hour);
  
  // 构建完整命盘
  const chart = buildCompleteChart(
    mingGong,
    shenGong,
    wuxingJu,
    majorStarsPlacement,
    auxiliaryStarsPlacement,
    maleficStarsPlacement
  );
  
  return {
    birthInfo: {
      year: year,
      month: month,
      day: day,
      hour: hour,
      isMale: isMale,
      lunarInfo: lunarInfo
    },
    mingGong: mingGong,
    shenGong: shenGong,
    wuxingJu: wuxingJu,
    chart: chart,
    majorStars: majorStarsPlacement,
    auxiliaryStars: auxiliaryStarsPlacement,
    maleficStars: maleficStarsPlacement
  };
}

/**
 * 计算农历信息（简化版本）
 */
function calculateLunarInfo(year, month, day) {
  // 简化处理：使用公历年份计算天干地支
  const stemIndex = (year - 4) % 10;
  const branchIndex = (year - 4) % 12;
  
  return {
    year: year,
    month: month,
    day: day,
    yearStem: HEAVENLY_STEMS[stemIndex],
    yearBranch: EARTHLY_BRANCHES[branchIndex],
    yearGanzhi: `${HEAVENLY_STEMS[stemIndex]}${EARTHLY_BRANCHES[branchIndex]}`
  };
}

/**
 * 计算命宫
 */
function calculateMingGong(month, hour) {
  // 命宫计算公式：寅宫起正月，顺数至生月，再从生月起子时，逆数至生时
  let mingGongIndex = (month - 1 + 2) % 12; // 寅宫起正月
  const hourBranch = Math.floor((hour + 1) / 2) % 12; // 时辰对应地支
  mingGongIndex = (mingGongIndex - hourBranch + 12) % 12;
  
  return {
    palace: TWELVE_PALACES[0], // 命宫
    branch: EARTHLY_BRANCHES[mingGongIndex],
    index: mingGongIndex
  };
}

/**
 * 计算身宫
 */
function calculateShenGong(month, hour) {
  // 身宫计算公式：寅宫起正月，顺数至生月，再从生月起子时，顺数至生时
  let shenGongIndex = (month - 1 + 2) % 12; // 寅宫起正月
  const hourBranch = Math.floor((hour + 1) / 2) % 12; // 时辰对应地支
  shenGongIndex = (shenGongIndex + hourBranch) % 12;
  
  return {
    branch: EARTHLY_BRANCHES[shenGongIndex],
    index: shenGongIndex
  };
}

/**
 * 计算五行局
 */
function calculateWuxingJu(year) {
  // 完整的六十甲子纳音五行局计算
  const wuxingJuTable = {
    '甲子': '海中金', '乙丑': '海中金',
    '丙寅': '炉中火', '丁卯': '炉中火',
    '戊辰': '大林木', '己巳': '大林木',
    '庚午': '路旁土', '辛未': '路旁土',
    '壬申': '剑锋金', '癸酉': '剑锋金',
    '甲戌': '山头火', '乙亥': '山头火',
    '丙子': '涧下水', '丁丑': '涧下水',
    '戊寅': '城头土', '己卯': '城头土',
    '庚辰': '白蜡金', '辛巳': '白蜡金',
    '壬午': '杨柳木', '癸未': '杨柳木',
    '甲申': '泉中水', '乙酉': '泉中水',
    '丙戌': '屋上土', '丁亥': '屋上土',
    '戊子': '霹雳火', '己丑': '霹雳火',
    '庚寅': '松柏木', '辛卯': '松柏木',
    '壬辰': '长流水', '癸巳': '长流水',
    '甲午': '砂中金', '乙未': '砂中金',
    '丙申': '山下火', '丁酉': '山下火',
    '戊戌': '平地木', '己亥': '平地木',
    '庚子': '壁上土', '辛丑': '壁上土',
    '壬寅': '金箔金', '癸卯': '金箔金',
    '甲辰': '覆灯火', '乙巳': '覆灯火',
    '丙午': '天河水', '丁未': '天河水',
    '戊申': '大驿土', '己酉': '大驿土',
    '庚戌': '钗钏金', '辛亥': '钗钏金',
    '壬子': '桑柘木', '癸丑': '桑柘木',
    '甲寅': '大溪水', '乙卯': '大溪水',
    '丙辰': '沙中土', '丁巳': '沙中土',
    '戊午': '天上火', '己未': '天上火',
    '庚申': '石榴木', '辛酉': '石榴木',
    '壬戌': '大海水', '癸亥': '大海水'
  };
  
  const stemIndex = (year - 4) % 10;
  const branchIndex = (year - 4) % 12;
  const ganzhi = `${HEAVENLY_STEMS[stemIndex]}${EARTHLY_BRANCHES[branchIndex]}`;
  
  return wuxingJuTable[ganzhi] || '水二局'; // 默认水二局
}

/**
 * 安主星
 */
function placeMajorStars(lunarInfo, mingGong, wuxingJu) {
  const placement = {};

  // 紫微星安法（根据五行局计算）
  const ziweiIndex = calculateZiweiPosition(lunarInfo, wuxingJu);
  placement['紫微'] = ziweiIndex;

  // 天机星安法（紫微顺行一宫）
  placement['天机'] = (ziweiIndex + 1) % 12;

  // 太阳星安法（按月份固定）
  placement['太阳'] = calculateTaiyangPosition(lunarInfo.month);

  // 武曲星安法（紫微逆行二宫）
  placement['武曲'] = (ziweiIndex - 2 + 12) % 12;

  // 天同星安法（紫微顺行三宫）
  placement['天同'] = (ziweiIndex + 3) % 12;

  // 廉贞星安法（紫微逆行四宫）
  placement['廉贞'] = (ziweiIndex - 4 + 12) % 12;

  // 天府星安法（与紫微相对）
  placement['天府'] = (ziweiIndex + 6) % 12;

  // 太阴星安法（与太阳相对）
  placement['太阴'] = (placement['太阳'] + 6) % 12;

  // 贪狼星安法（紫微顺行四宫）
  placement['贪狼'] = (ziweiIndex + 4) % 12;

  // 巨门星安法（紫微顺行二宫）
  placement['巨门'] = (ziweiIndex + 2) % 12;

  return placement;
}

/**
 * 计算紫微星位置
 */
function calculateZiweiPosition(lunarInfo, wuxingJu) {
  // 紫微星安法：根据五行局和农历日期计算
  const day = lunarInfo.day;

  // 五行局对应的起始宫位
  const wuxingJuStartPalace = {
    '水二局': 2,  // 寅宫
    '木三局': 2,  // 寅宫
    '金四局': 2,  // 寅宫
    '土五局': 2,  // 寅宫
    '火六局': 2   // 寅宫
  };

  // 五行局对应的局数
  const juNumber = {
    '水二局': 2,
    '木三局': 3,
    '金四局': 4,
    '土五局': 5,
    '火六局': 6
  };

  // 获取五行局类型（从纳音中提取）
  let ju = 2; // 默认水二局
  if (wuxingJu.includes('水')) ju = 2;
  else if (wuxingJu.includes('木')) ju = 3;
  else if (wuxingJu.includes('金')) ju = 4;
  else if (wuxingJu.includes('土')) ju = 5;
  else if (wuxingJu.includes('火')) ju = 6;

  // 紫微星位置计算：(农历日期 - 1) / 局数，余数决定宫位
  const remainder = (day - 1) % (ju * 10);
  const palaceIndex = Math.floor(remainder / ju);

  return (2 + palaceIndex) % 12; // 从寅宫开始
}

/**
 * 计算太阳星位置
 */
function calculateTaiyangPosition(month) {
  // 太阳星按月份安宫
  const taiyangTable = [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // 子丑寅卯...
  return taiyangTable[month - 1];
}

/**
 * 安辅星
 */
function placeAuxiliaryStars(lunarInfo, hour) {
  const placement = {};
  
  // 左辅右弼安法
  const stemIndex = HEAVENLY_STEMS.indexOf(lunarInfo.yearStem);
  placement['左辅'] = (stemIndex + 4) % 12;
  placement['右弼'] = (stemIndex + 10) % 12;
  
  // 文昌文曲安法
  const hourBranch = Math.floor((hour + 1) / 2) % 12;
  placement['文昌'] = (hourBranch + 10) % 12;
  placement['文曲'] = (hourBranch + 4) % 12;
  
  return placement;
}

/**
 * 安煞星
 */
function placeMaleficStars(lunarInfo, hour) {
  const placement = {};
  
  // 擎羊陀罗安法
  const stemIndex = HEAVENLY_STEMS.indexOf(lunarInfo.yearStem);
  placement['擎羊'] = (stemIndex + 1) % 12;
  placement['陀罗'] = (stemIndex - 1 + 12) % 12;
  
  // 火星铃星安法
  const branchIndex = EARTHLY_BRANCHES.indexOf(lunarInfo.yearBranch);
  placement['火星'] = (branchIndex + 2) % 12;
  placement['铃星'] = (branchIndex + 10) % 12;
  
  return placement;
}

/**
 * 构建完整命盘
 */
function buildCompleteChart(mingGong, shenGong, wuxingJu, majorStars, auxiliaryStars, maleficStars) {
  const chart = {};
  
  // 初始化十二宫
  for (let i = 0; i < 12; i++) {
    const palaceIndex = (mingGong.index + i) % 12;
    chart[TWELVE_PALACES[i]] = {
      branch: EARTHLY_BRANCHES[palaceIndex],
      index: palaceIndex,
      stars: [],
      isMingGong: i === 0,
      isShenGong: palaceIndex === shenGong.index
    };
  }
  
  // 安放主星
  Object.keys(majorStars).forEach(star => {
    const position = majorStars[star];
    const palace = findPalaceByIndex(chart, position);
    if (palace) {
      palace.stars.push({
        name: star,
        type: 'major',
        ...MAJOR_STARS[star]
      });
    }
  });
  
  // 安放辅星
  Object.keys(auxiliaryStars).forEach(star => {
    const position = auxiliaryStars[star];
    const palace = findPalaceByIndex(chart, position);
    if (palace) {
      palace.stars.push({
        name: star,
        type: 'auxiliary',
        ...AUXILIARY_STARS[star]
      });
    }
  });
  
  // 安放煞星
  Object.keys(maleficStars).forEach(star => {
    const position = maleficStars[star];
    const palace = findPalaceByIndex(chart, position);
    if (palace) {
      palace.stars.push({
        name: star,
        type: 'malefic',
        ...MALEFIC_STARS[star]
      });
    }
  });
  
  return chart;
}

/**
 * 根据地支索引查找宫位
 */
function findPalaceByIndex(chart, index) {
  for (const palace of Object.values(chart)) {
    if (palace.index === index) {
      return palace;
    }
  }
  return null;
}

/**
 * 格式化紫微命盘显示
 */
export function formatZiweiChart(ziweiData) {
  const chart = ziweiData.chart;
  const formatted = {};
  
  Object.keys(chart).forEach(palaceName => {
    const palace = chart[palaceName];
    formatted[palaceName] = {
      branch: palace.branch,
      stars: palace.stars.map(star => star.name).join('、') || '空宫',
      isMingGong: palace.isMingGong,
      isShenGong: palace.isShenGong,
      majorStars: palace.stars.filter(s => s.type === 'major').map(s => s.name),
      auxiliaryStars: palace.stars.filter(s => s.type === 'auxiliary').map(s => s.name),
      maleficStars: palace.stars.filter(s => s.type === 'malefic').map(s => s.name)
    };
  });
  
  return formatted;
}
