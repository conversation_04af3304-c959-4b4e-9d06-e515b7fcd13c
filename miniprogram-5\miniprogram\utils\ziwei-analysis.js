// 紫微斗数分析系统
// 基于传统紫微斗数理论

import { TWELVE_PALACES, MAJOR_STARS, AUXILIARY_STARS, MALEFIC_STARS } from './ziwei-calculator.js';

// 星曜组合格局
const STAR_PATTERNS = {
  '紫微天府': { name: '紫府同宫', level: '上格', meaning: '帝王之相，富贵双全' },
  '紫微贪狼': { name: '紫贪同宫', level: '中格', meaning: '先贫后富，晚年发达' },
  '紫微七杀': { name: '紫杀同宫', level: '中格', meaning: '威权显赫，但多波折' },
  '紫微破军': { name: '紫破同宫', level: '下格', meaning: '开创力强，但不稳定' },
  '天机太阴': { name: '机月同梁', level: '上格', meaning: '聪明智慧，善于谋略' },
  '天同太阴': { name: '同阴朝斗', level: '上格', meaning: '温和富贵，安享天年' },
  '武曲天府': { name: '武府同宫', level: '上格', meaning: '财官双美，富贵之命' },
  '武曲贪狼': { name: '武贪同宫', level: '中格', meaning: '横发横破，财来财去' },
  '武曲七杀': { name: '武杀同宫', level: '中格', meaning: '将相之材，但多辛劳' }
};

// 宫位重要性权重
const PALACE_WEIGHTS = {
  '命宫': 10,
  '财帛宫': 8,
  '官禄宫': 8,
  '夫妻宫': 7,
  '福德宫': 6,
  '田宅宫': 6,
  '子女宫': 5,
  '疾厄宫': 5,
  '迁移宫': 4,
  '兄弟宫': 3,
  '奴仆宫': 3,
  '父母宫': 4
};

/**
 * 综合分析紫微命盘
 * @param {Object} ziweiData - 紫微斗数数据
 * @returns {Object} 综合分析结果
 */
function comprehensiveZiweiAnalysis(ziweiData) {
  const chart = ziweiData.chart;
  
  // 分析命宫
  const mingGongAnalysis = analyzeMingGong(chart['命宫']);
  
  // 分析格局
  const patternAnalysis = analyzePatterns(chart);
  
  // 分析星曜强弱
  const starStrengthAnalysis = analyzeStarStrength(chart);
  
  // 分析三方四正
  const sanfangsizhengAnalysis = analyzeSanfangsizheng(chart);
  
  // 分析各宫位
  const palaceAnalysis = analyzePalaces(chart);
  
  // 综合评分
  const overallScore = calculateOverallScore(chart);
  
  return {
    mingGong: mingGongAnalysis,
    patterns: patternAnalysis,
    starStrength: starStrengthAnalysis,
    sanfangsizheng: sanfangsizhengAnalysis,
    palaces: palaceAnalysis,
    overallScore: overallScore,
    summary: generateSummary(mingGongAnalysis, patternAnalysis, overallScore)
  };
}

/**
 * 分析命宫
 */
function analyzeMingGong(mingGong) {
  const analysis = {
    branch: mingGong.branch,
    majorStars: [],
    auxiliaryStars: [],
    maleficStars: [],
    strength: 0,
    nature: '',
    characteristics: []
  };
  
  // 分类星曜
  mingGong.stars.forEach(star => {
    if (star.type === 'major') {
      analysis.majorStars.push(star.name);
      analysis.strength += star.brightness;
    } else if (star.type === 'auxiliary') {
      analysis.auxiliaryStars.push(star.name);
      analysis.strength += star.brightness;
    } else if (star.type === 'malefic') {
      analysis.maleficStars.push(star.name);
      analysis.strength -= star.brightness;
    }
  });
  
  // 判断命宫性质
  if (analysis.majorStars.includes('紫微')) {
    analysis.nature = '帝王命格';
    analysis.characteristics.push('天生领导才能', '喜欢掌控全局', '有王者风范');
  } else if (analysis.majorStars.includes('天机')) {
    analysis.nature = '智慧命格';
    analysis.characteristics.push('聪明机智', '善于思考', '适合策划工作');
  } else if (analysis.majorStars.includes('太阳')) {
    analysis.nature = '贵人命格';
    analysis.characteristics.push('光明磊落', '乐于助人', '容易得到贵人相助');
  } else if (analysis.majorStars.includes('武曲')) {
    analysis.nature = '财富命格';
    analysis.characteristics.push('理财能力强', '适合经商', '财运较佳');
  } else {
    analysis.nature = '平常命格';
    analysis.characteristics.push('性格平和', '生活稳定', '需要后天努力');
  }
  
  return analysis;
}

/**
 * 分析格局
 */
function analyzePatterns(chart) {
  const patterns = [];
  const mingGong = chart['命宫'];
  
  // 检查命宫星曜组合
  const majorStarsInMing = mingGong.stars
    .filter(s => s.type === 'major')
    .map(s => s.name)
    .sort();
  
  // 检查是否有特殊格局
  if (majorStarsInMing.length >= 2) {
    const combination = majorStarsInMing.join('');
    if (STAR_PATTERNS[combination]) {
      patterns.push(STAR_PATTERNS[combination]);
    }
  }
  
  // 检查单星格局
  majorStarsInMing.forEach(star => {
    if (star === '紫微') {
      patterns.push({
        name: '紫微独坐',
        level: '上格',
        meaning: '独当一面，自立自强'
      });
    } else if (star === '天府') {
      patterns.push({
        name: '天府独坐',
        level: '上格',
        meaning: '保守稳重，财库丰厚'
      });
    }
  });
  
  // 检查辅星格局
  const auxiliaryStars = mingGong.stars
    .filter(s => s.type === 'auxiliary')
    .map(s => s.name);
  
  if (auxiliaryStars.includes('左辅') && auxiliaryStars.includes('右弼')) {
    patterns.push({
      name: '左右同宫',
      level: '上格',
      meaning: '得力助手多，事业顺利'
    });
  }
  
  if (auxiliaryStars.includes('文昌') && auxiliaryStars.includes('文曲')) {
    patterns.push({
      name: '昌曲同宫',
      level: '上格',
      meaning: '文采出众，利于考试'
    });
  }
  
  return patterns;
}

/**
 * 分析星曜强弱
 */
function analyzeStarStrength(chart) {
  const analysis = {
    strongStars: [],
    weakStars: [],
    balancedStars: []
  };
  
  Object.keys(chart).forEach(palaceName => {
    const palace = chart[palaceName];
    palace.stars.forEach(star => {
      if (star.brightness >= 4) {
        analysis.strongStars.push({
          name: star.name,
          palace: palaceName,
          strength: star.brightness
        });
      } else if (star.brightness <= 2) {
        analysis.weakStars.push({
          name: star.name,
          palace: palaceName,
          strength: star.brightness
        });
      } else {
        analysis.balancedStars.push({
          name: star.name,
          palace: palaceName,
          strength: star.brightness
        });
      }
    });
  });
  
  return analysis;
}

/**
 * 分析三方四正
 */
function analyzeSanfangsizheng(chart) {
  // 三方四正：命宫、财帛宫、官禄宫、迁移宫
  const sanfangPalaces = ['命宫', '财帛宫', '官禄宫', '迁移宫'];
  const analysis = {
    totalStars: 0,
    majorStars: [],
    auxiliaryStars: [],
    maleficStars: [],
    strength: 0
  };
  
  sanfangPalaces.forEach(palaceName => {
    const palace = chart[palaceName];
    if (palace) {
      palace.stars.forEach(star => {
        analysis.totalStars++;
        if (star.type === 'major') {
          analysis.majorStars.push(star.name);
          analysis.strength += star.brightness;
        } else if (star.type === 'auxiliary') {
          analysis.auxiliaryStars.push(star.name);
          analysis.strength += star.brightness * 0.7;
        } else if (star.type === 'malefic') {
          analysis.maleficStars.push(star.name);
          analysis.strength -= star.brightness;
        }
      });
    }
  });
  
  // 判断三方四正强弱
  if (analysis.strength >= 15) {
    analysis.level = '强';
    analysis.description = '三方四正星曜有力，格局较高';
  } else if (analysis.strength >= 8) {
    analysis.level = '中';
    analysis.description = '三方四正星曜平衡，格局中等';
  } else {
    analysis.level = '弱';
    analysis.description = '三方四正星曜不足，需要后天努力';
  }
  
  return analysis;
}

/**
 * 分析各宫位
 */
function analyzePalaces(chart) {
  const analysis = {};
  
  Object.keys(chart).forEach(palaceName => {
    const palace = chart[palaceName];
    const palaceAnalysis = {
      stars: palace.stars.map(s => s.name),
      strength: 0,
      nature: '',
      advice: ''
    };
    
    // 计算宫位强度
    palace.stars.forEach(star => {
      if (star.type === 'major') {
        palaceAnalysis.strength += star.brightness;
      } else if (star.type === 'auxiliary') {
        palaceAnalysis.strength += star.brightness * 0.7;
      } else if (star.type === 'malefic') {
        palaceAnalysis.strength -= star.brightness;
      }
    });
    
    // 根据宫位给出建议
    palaceAnalysis.advice = getPalaceAdvice(palaceName, palaceAnalysis.strength, palace.stars);
    
    analysis[palaceName] = palaceAnalysis;
  });
  
  return analysis;
}

/**
 * 获取宫位建议
 */
function getPalaceAdvice(palaceName, strength, stars) {
  const starNames = stars.map(s => s.name);
  
  switch (palaceName) {
    case '命宫':
      return strength >= 8 ? '命格较高，天生条件好' : '需要后天努力提升自己';
    case '财帛宫':
      return starNames.includes('武曲') ? '财运极佳，适合投资理财' : 
             strength >= 6 ? '财运不错，稳健理财' : '需要开源节流';
    case '官禄宫':
      return starNames.includes('紫微') ? '适合管理职位，前途光明' :
             strength >= 6 ? '事业运佳，宜积极进取' : '需要踏实工作';
    case '夫妻宫':
      return strength >= 6 ? '感情运佳，婚姻美满' : '感情需要用心经营';
    default:
      return strength >= 6 ? '此宫位运势较好' : '此宫位需要注意';
  }
}

/**
 * 计算综合评分
 */
function calculateOverallScore(chart) {
  let totalScore = 0;
  let maxScore = 0;
  
  Object.keys(chart).forEach(palaceName => {
    const palace = chart[palaceName];
    const weight = PALACE_WEIGHTS[palaceName] || 1;
    let palaceScore = 0;
    
    palace.stars.forEach(star => {
      if (star.type === 'major') {
        palaceScore += star.brightness * 2;
      } else if (star.type === 'auxiliary') {
        palaceScore += star.brightness;
      } else if (star.type === 'malefic') {
        palaceScore -= star.brightness;
      }
    });
    
    totalScore += palaceScore * weight;
    maxScore += 10 * weight; // 假设最高分为10
  });
  
  const percentage = Math.max(0, Math.min(100, (totalScore / maxScore) * 100));
  
  return {
    score: Math.round(percentage),
    level: percentage >= 80 ? '优秀' : 
           percentage >= 60 ? '良好' : 
           percentage >= 40 ? '一般' : '需要努力',
    description: getScoreDescription(percentage)
  };
}

/**
 * 获取评分描述
 */
function getScoreDescription(score) {
  if (score >= 80) {
    return '命格优秀，天生条件好，容易获得成功';
  } else if (score >= 60) {
    return '命格良好，具备成功的基础，需要适当努力';
  } else if (score >= 40) {
    return '命格一般，需要通过后天努力来改善运势';
  } else {
    return '命格较弱，需要更多的努力和智慧来创造成功';
  }
}

/**
 * 生成综合总结
 */
function generateSummary(mingGongAnalysis, patternAnalysis, overallScore) {
  let summary = `您的命格属于${mingGongAnalysis.nature}，`;
  
  if (patternAnalysis.length > 0) {
    const topPattern = patternAnalysis[0];
    summary += `具有${topPattern.name}格局，${topPattern.meaning}。`;
  } else {
    summary += `星曜配置较为平常，需要后天努力。`;
  }
  
  summary += `综合评分${overallScore.score}分，${overallScore.description}`;

  return summary;
}

module.exports = {
  STAR_PATTERNS,
  comprehensiveZiweiAnalysis
};
